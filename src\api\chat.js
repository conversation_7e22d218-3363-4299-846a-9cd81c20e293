import request from '@/utils/request'
import { getToken } from '@/utils/auth'


// 普通对话
export function chatCall(message, sessionId) {
  return request({
    url: '/chat/call',
    method: 'post',
    timeout: 30000, // 30秒超时
    params: {
      message: message,
      sessionId: sessionId
    }
  })
}

// 流式对话 - 使用自定义实现替代EventSource以支持认证头
export function chatStream(message, sessionId, streamId) {
  const token = getToken()
  const url = `chat/stream/${streamId}?message=${encodeURIComponent(message)}&sessionId=${sessionId || ''}`

  // 创建一个模拟EventSource的对象
  const mockEventSource = {
    onmessage: null,
    onerror: null,
    onopen: null,
    readyState: 0,
    url: url,
    close: function() {
      if (this.controller) {
        this.controller.abort()
      }
      this.readyState = 2
    }
  }

  // 使用fetch API进行流式请求
  const controller = new AbortController()
  mockEventSource.controller = controller

  const headers = {
    'Accept': 'text/event-stream',
    'Cache-Control': 'no-cache'
  }

  // 添加认证头
  if (token) {
    headers['token'] = token
    headers['Authorization'] = `Bearer ${token}`
  }

  fetch(url, {
    method: 'POST',
    headers: headers,
    signal: controller.signal
  }).then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    mockEventSource.readyState = 1
    if (mockEventSource.onopen) {
      mockEventSource.onopen()
    }

    const reader = response.body.getReader()
    const decoder = new TextDecoder()

    let buffer = ''

    function readStream() {
      reader.read().then(({ done, value }) => {
        if (done) {
          mockEventSource.readyState = 2
          // 流式连接结束时，发送结束信号
          if (mockEventSource.onmessage) {
            mockEventSource.onmessage({ data: '[DONE]' })
          }
          return
        }

        // 将新数据添加到缓冲区
        buffer += decoder.decode(value, { stream: true })

        // 按行分割处理
        const lines = buffer.split('\n')

        // 保留最后一行（可能不完整）
        buffer = lines.pop() || ''

        // 处理完整的行
        let currentEvent = null
        let currentData = null

        for (const line of lines) {
          const trimmedLine = line.trim()

          if (trimmedLine.startsWith('event:')) {
            currentEvent = trimmedLine.substring(6).trim()
          } else if (trimmedLine.startsWith('data:')) {
            currentData = trimmedLine.substring(5).trim()

            // 当有事件和数据时，触发消息
            if (currentEvent === 'message' && currentData && mockEventSource.onmessage) {
              mockEventSource.onmessage({ data: currentData })
            } else if (currentEvent === 'close' || currentEvent === 'end' || currentEvent === 'done') {
              // 处理结束事件
              if (mockEventSource.onmessage) {
                mockEventSource.onmessage({ data: '[DONE]' })
              }
            } else if (currentData && mockEventSource.onmessage) {
              // 没有明确事件类型时，直接发送数据
              mockEventSource.onmessage({ data: currentData })
            }

            // 重置状态
            currentEvent = null
            currentData = null
          } else if (trimmedLine === '' && currentData !== null) {
            // 空行表示事件结束，如果有数据则触发
            if (currentData && mockEventSource.onmessage) {
              mockEventSource.onmessage({ data: currentData })
            }
            currentEvent = null
            currentData = null
          }
        }

        readStream()
      }).catch(error => {
        if (mockEventSource.onerror) {
          mockEventSource.onerror(error)
        }
      })
    }

    readStream()
  }).catch(error => {
    if (mockEventSource.onerror) {
      mockEventSource.onerror(error)
    }
  })

  return mockEventSource
}

// 终止流式输出
export function stopStream(streamId) {
  return request({
    url: `/chat/stopStream/${streamId}`,
    method: 'delete'
  })
}