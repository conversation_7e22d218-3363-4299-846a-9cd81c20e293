<template>
  <div class="app-container">
    <div class="page-header">
      <h2>链接管理</h2>
      <!-- <p>管理和组织您的链接收藏</p> -->
    </div>

    <!-- 搜索和操作区域 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyWord"
        placeholder="搜索关键词..."
        clearable
        class="filter-item"
        style="width: 200px"
      />
      <el-select
        v-model="listQuery.category"
        placeholder="选择分类"
        clearable
        class="filter-item"
        style="width: 150px; margin-left: 10px;"
      >
        <el-option label="百度" value="百度" />
        <el-option label="谷歌" value="谷歌" />
        <el-option label="搜狗" value="搜狗" />
        <el-option label="雅虎" value="雅虎" />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
        style="margin-left: 10px;"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
        style="margin-left: 10px;"
      >
        添加链接
      </el-button>
      <el-button
        class="filter-item"
        type="success"
        icon="el-icon-upload2"
        @click="handleImport"
        style="margin-left: 10px;"
      >
        导入Excel
      </el-button>
      <el-button
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        @click="handleBatchDelete"
        style="margin-left: 10px;"
        :disabled="selectedLinks.length === 0"
      >
        批量删除
      </el-button>
    </div>

    <!-- 链接列表表格 -->
    <el-table
      style="margin-top: 20px;"
      v-loading="listLoading"
      element-loading-text="Loading"
      border
      :data="linkList"
      fit
      highlight-current-row
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" width="80" align="center" prop="linkId" />
      <el-table-column label="关键词" width="150" align="center" prop="keyWord" />
      <el-table-column label="链接地址" align="center" min-width="300">
        <template #default="{ row }">
          <el-link :href="row.url" target="_blank" type="primary" class="link-url">
            {{ row.url }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="分类" width="120" align="center">
        <template #default="{ row }">
          <el-tag :type="getCategoryTagType(row.category)">{{ row.category || '未分类' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="180">
        <template #default="{ row }">
          <span>{{ formatTime(row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" width="180">
        <template #default="{ row }">
          <span>{{ formatTime(row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template #default="{ row }">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">编辑</el-button>
          <!-- <el-button type="success" size="mini" @click="openLink(row.url)">访问</el-button> -->
          <el-button type="danger" size="mini" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="linkList.length > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="fetchData"
    />

    <!-- 添加/编辑链接对话框 -->
    <el-dialog
      :title="dialogStatus === 'create' ? '添加链接' : '编辑链接'"
      :visible.sync="dialogFormVisible"
      width="600px"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="100px"
        style="width: 500px; margin-left:50px;"
      >
        <el-form-item label="关键词" prop="keyWord">
          <el-input v-model="temp.keyWord" placeholder="请输入关键词" />
        </el-form-item>
        <el-form-item label="链接地址" prop="url">
          <el-input v-model="temp.url" placeholder="请输入完整的URL地址" />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-select v-model="temp.category" placeholder="请选择分类" style="width: 100%;">
            <el-option label="百度" value="百度" />
            <el-option label="谷歌" value="谷歌" />
            <el-option label="搜狗" value="搜狗" />
            <el-option label="雅虎" value="雅虎" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- Excel导入对话框 -->
    <el-dialog
      title="导入Excel文件"
      :visible.sync="importDialogVisible"
      width="500px"
    >
      <el-upload
        class="upload-demo"
        drag
        :action="uploadAction"
        :headers="uploadHeaders"
        :before-upload="beforeUpload"
        :on-success="handleImportSuccess"
        :on-error="handleImportError"
        :file-list="fileList"
        accept=".xlsx,.xls"
        :limit="1"
        :on-exceed="handleExceed"
        :auto-upload="false"
        ref="upload"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将Excel文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">
          只能上传xlsx/xls文件，且不超过10MB
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="importDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitUpload">确定导入</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getLinkList, insertLink, deleteLink, updateLink, batchDeleteLinks, importLinksByExcel } from '@/api/link'
import { Message, MessageBox } from 'element-ui'
import Pagination from '@/components/Pagination.vue'
import { getToken } from '@/utils/auth'

export default {
  name: 'LinkManagement',
  components: {
    Pagination
  },
  data() {
    return {
      linkList: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
        keyWord: '',
        category: ''
      },
      selectedLinks: [],
      dialogFormVisible: false,
      dialogStatus: '',
      temp: {
        linkId: undefined,
        keyWord: '',
        url: '',
        category: ''
      },
      rules: {
        keyWord: [
          { required: true, message: '请输入关键词', trigger: 'blur' },
          { min: 1, max: 50, message: '关键词长度在1到50个字符', trigger: 'blur' }
        ],
        url: [
          { required: true, message: '请输入链接地址', trigger: 'blur' },
          { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择分类', trigger: 'change' }
        ]
      },
      importDialogVisible: false,
      fileList: [],
      uploadAction: process.env.VUE_APP_BASE_API + '/link/importLinksByExcel',
      uploadHeaders: {
        'token': getToken()
      }
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    // 获取链接列表
    async fetchData() {
      this.listLoading = true
      try {
        const response = await getLinkList(
          this.listQuery.keyWord,
          this.listQuery.category,
          this.listQuery.page,
          this.listQuery.limit
        )
        if (response.code === 20000) {
          // 适配后端响应格式：数据在 data.list 中，总数在 data.total 中
          this.linkList = response.data?.list || []
          this.total = response.data?.total || 0
        } else {
          this.linkList = []
          this.total = 0
          Message({
            message: response.message || '获取链接列表失败',
            type: 'warning'
          })
        }
      } catch (error) {
        console.error('获取链接列表失败:', error)
        Message({
          message: '获取链接列表失败',
          type: 'error'
        })
        this.linkList = []
        this.total = 0
      } finally {
        this.listLoading = false
      }
    },

    // 搜索过滤
    handleFilter() {
      this.listQuery.page = 1
      this.fetchData()
    },

    // 重置临时数据
    resetTemp() {
      this.temp = {
        linkId: undefined,
        keyWord: '',
        url: '',
        category: ''
      }
    },

    // 新建链接
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },

    // 创建链接
    createData() {
      this.$refs['dataForm'].validate(async (valid) => {
        if (valid) {
          try {
            const response = await insertLink(this.temp)
            if (response.code === 20000) {
              Message({
                message: '链接创建成功',
                type: 'success'
              })
              this.dialogFormVisible = false
              this.fetchData()
            } else {
              Message({
                message: response.message || '创建链接失败',
                type: 'error'
              })
            }
          } catch (error) {
            console.error('创建链接失败:', error)
            Message({
              message: '创建链接失败',
              type: 'error'
            })
          }
        }
      })
    },

    // 编辑链接
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },

    // 更新链接数据
    updateData() {
      this.$refs['dataForm'].validate(async (valid) => {
        if (valid) {
          try {
            const response = await updateLink(this.temp)
            if (response.code === 20000) {
              Message({
                message: '链接更新成功',
                type: 'success'
              })
              this.dialogFormVisible = false
              this.fetchData()
            } else {
              Message({
                message: response.message || '更新链接失败',
                type: 'error'
              })
            }
          } catch (error) {
            console.error('更新链接失败:', error)
            Message({
              message: '更新链接失败',
              type: 'error'
            })
          }
        }
      })
    },

    // 删除单个链接
    handleDelete(row) {
      MessageBox.confirm(
        `确定要删除链接 "${row.keyWord}" 吗？`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          const response = await deleteLink(row.linkId)
          if (response.code === 20000) {
            Message({
              message: '删除成功',
              type: 'success'
            })
            this.fetchData()
          } else {
            Message({
              message: response.message || '删除失败',
              type: 'error'
            })
          }
        } catch (error) {
          console.error('删除链接失败:', error)
          Message({
            message: '删除失败',
            type: 'error'
          })
        }
      }).catch(() => {
        Message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedLinks.length === 0) {
        Message({
          message: '请选择要删除的链接',
          type: 'warning'
        })
        return
      }

      MessageBox.confirm(
        `确定要删除选中的 ${this.selectedLinks.length} 个链接吗？`,
        '确认批量删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          const linkIds = this.selectedLinks.map(link => link.linkId)
          const response = await batchDeleteLinks(linkIds)
          if (response.code === 20000) {
            Message({
              message: response.data || '批量删除成功',
              type: 'success'
            })
            this.fetchData()
          } else {
            Message({
              message: response.message || '批量删除失败',
              type: 'error'
            })
          }
        } catch (error) {
          console.error('批量删除失败:', error)
          Message({
            message: '批量删除失败',
            type: 'error'
          })
        }
      }).catch(() => {
        Message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },

    // 选择变化处理
    handleSelectionChange(val) {
      this.selectedLinks = val
    },

    // 打开链接
    openLink(url) {
      if (url) {
        window.open(url, '_blank')
      } else {
        Message({
          message: '链接地址无效',
          type: 'warning'
        })
      }
    },

    // 导入Excel
    handleImport() {
      this.importDialogVisible = true
      this.fileList = []
    },

    // 上传前检查
    beforeUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                     file.type === 'application/vnd.ms-excel' ||
                     file.name.endsWith('.xlsx') ||
                     file.name.endsWith('.xls')
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isExcel) {
        Message.error('只能上传Excel文件!')
        return false
      }
      if (!isLt10M) {
        Message.error('上传文件大小不能超过 10MB!')
        return false
      }
      return true
    },

    // 提交上传
    submitUpload() {
      this.$refs.upload.submit()
    },

    // 导入成功
    handleImportSuccess(response, file) {
      if (response.code === 20000) {
        Message.success(`导入成功，共导入 ${response.data.length} 条链接`)
        this.importDialogVisible = false
        this.fileList = []
        this.fetchData()
      } else {
        Message.error(response.message || '导入失败')
      }
    },

    // 导入失败
    handleImportError(err, file) {
      console.error('导入失败:', err)
      Message.error('导入失败，请重试')
    },

    // 文件数量超出限制
    handleExceed(files, fileList) {
      Message.warning('只能上传一个文件，请先删除已上传的文件')
    },

    // 获取分类标签类型
    getCategoryTagType(category) {
      switch (category) {
        case '百度':
          return 'primary'
        case '谷歌':
          return 'success'
        case '搜狗':
          return 'info'
        case '雅虎':
          return 'warning'
        default:
          return 'info'
      }
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-item {
  margin-right: 10px;
}

.link-url {
  max-width: 280px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

.dialog-footer {
  text-align: right;
}

.upload-demo {
  width: 100%;
}

.el-table {
  margin-top: 20px;
}
</style>