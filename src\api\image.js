import request from '@/utils/request'

// 查询图片列表
export function getImageList(category, description, pageNum, pageSize) {
  return request({
    url: '/image/getImageList',
    method: 'get',
    params: {
      category: category,
      description: description,
      pageNum: pageNum,
      pageSize: pageSize
    }
  })
}

// 新增图片
export function insertImage(data) {
  return request({
    url: '/image/insertImage',
    method: 'post',
    data
  })
}

// 删除图片
export function deleteImage(imageId) {
  return request({
    url: `/image/deleteImage/${imageId}`,
    method: 'delete'
  })
}

// 更新图片
export function updateImage(data) {
  return request({
    url: '/image/updateImage',
    method: 'post',
    data
  })
}

// 批量删除图片
export function batchDeleteImages(imageIds) {
  return request({
    url: '/image/batchDeleteImages',
    method: 'delete',
    data: imageIds
  })
}

// 批量导入图片（Excel）
export function importImagesByExcel(file) {
  const formData = new FormData()
  formData.append('excel', file)

  return request({
    url: '/image/importImagesByExcel',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 批量上传图片
export function batchUploadImages(category, description, files) {
  const formData = new FormData()
  formData.append('category', category)
  formData.append('description', description)

  // 添加多个文件
  for (let i = 0; i < files.length; i++) {
    formData.append('files', files[i])
  }

  return request({
    url: '/image/batchUploadImages',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}