import request from '@/utils/request'

// 查询链接列表
export function getLinkList(keyWord, category, pageNum, pageSize) {
  return request({
    url: '/link/getLinkList',
    method: 'get',
    params: {
      keyWord: keyWord,
      category: category,
      pageNum: pageNum,
      pageSize: pageSize
    }
  })
}

// 新增链接
export function insertLink(data) {
  return request({
    url: '/link/insertLink',
    method: 'post',
    data
  })
}

// 删除链接
export function deleteLink(linkId) {
  return request({
    url: `/link/deleteLink/${linkId}`,
    method: 'delete'
  })
}

// 更新链接
export function updateLink(data) {
  return request({
    url: '/link/updateLink',
    method: 'post',
    data
  })
}

// 批量删除链接
export function batchDeleteLinks(linkIds) {
  return request({
    url: '/link/batchDeleteLinks',
    method: 'delete',
    data: linkIds
  })
}

// 批量导入链接（Excel）
export function importLinksByExcel(file) {
  const formData = new FormData()
  formData.append('excel', file)

  return request({
    url: '/link/importLinksByExcel',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}