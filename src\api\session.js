import request from '@/utils/request'

// 新建会话
export function insertSession(message) {
  return request({
    url: '/chatSession/insertSession',
    method: 'post',
    params: {
      message: message
    }
  })
}

// 删除会话
export function deleteSession(sessionId) {
  return request({
    url: `/chatSession/deleteSession/${sessionId}`,
    method: 'delete'
  })
}

// 获取当前用户的会话列表
export function getChatSessionByUserId() {
  return request({
    url: '/chatSession/getChatSessionByUserId',
    method: 'get'
  })
}