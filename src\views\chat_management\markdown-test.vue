<template>
  <div class="markdown-test-container">
    <div class="test-header">
      <h2>Markdown 渲染测试</h2>
      <p>这个页面用于测试 AI 消息的 Markdown 渲染效果</p>
    </div>
    
    <div class="test-content">
      <div class="test-section">
        <h3>测试消息</h3>
        <div class="message-preview">
          <div class="message-item assistant">
            <div class="message-wrapper">
              <div class="message-avatar">
                <img src="/avatar/ai.png" alt="AI Assistant" class="avatar-image" />
              </div>
              <div class="message-content">
                <div class="message-text markdown-content" v-html="formatMessage(testMarkdown)"></div>
                <div class="message-time">{{ new Date().toLocaleTimeString() }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="test-section">
        <h3>原始 Markdown 文本</h3>
        <el-input
          type="textarea"
          v-model="testMarkdown"
          :rows="15"
          placeholder="输入 Markdown 文本进行测试..."
        />
      </div>
    </div>
  </div>
</template>

<script>
import { parseMarkdown } from '@/utils/markdown'

export default {
  name: 'MarkdownTest',
  data() {
    return {
      testMarkdown: `# 这是一个标题

## 二级标题

这是一个普通段落，包含一些**粗体文本**和*斜体文本*。

### 代码示例

这是一个内联代码：\`console.log('Hello World')\`

这是一个代码块：

\`\`\`javascript
function greet(name) {
  console.log(\`Hello, \${name}!\`);
}

greet('World');
\`\`\`

### 列表

无序列表：
- 第一项
- 第二项
- 第三项

有序列表：
1. 第一步
2. 第二步
3. 第三步

### 引用

> 这是一个引用块
> 可以包含多行内容

### 链接和图片

这是一个[链接](https://example.com)

### 表格

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |

### 分割线

---

### 其他格式

~~删除线文本~~

**粗体文本**

*斜体文本*`
    }
  },
  methods: {
    formatMessage(content) {
      if (!content) return ''
      return parseMarkdown(content)
    }
  }
}
</script>

<style scoped>
@import './markdown.css';

.markdown-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
}

.test-header h2 {
  color: #303133;
  margin-bottom: 10px;
}

.test-header p {
  color: #606266;
  font-size: 14px;
}

.test-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.test-section h3 {
  color: #303133;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
}

.message-preview {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background: white;
  min-height: 400px;
  overflow-y: auto;
}

/* 复用聊天页面的消息样式 */
.message-item {
  display: flex;
  margin-bottom: 20px;
  width: 100%;
}

.message-item.assistant {
  justify-content: flex-start;
}

.message-item.assistant .message-wrapper {
  display: flex;
  align-items: flex-start;
  max-width: 100%;
  gap: 12px;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  overflow: hidden;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.message-content {
  flex: 1;
  min-width: 200px;
}

.message-text {
  background: #f0f0f0;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 14px;
  line-height: 1.5;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.message-time {
  font-size: 12px;
  color: #c0c4cc;
  margin-top: 5px;
}

@media (max-width: 768px) {
  .test-content {
    grid-template-columns: 1fr;
  }
}
</style>
