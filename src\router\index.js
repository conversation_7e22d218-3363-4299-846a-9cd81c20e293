import Vue from 'vue'
import Router from 'vue-router'
import Layout from '@/layout'

Vue.use(Router)

// 路由配置常量
const ROUTE_META = {
  SYSTEM : {
    title: '系统管理',
    icon: 'el-icon-setting'
  },
  USER : {
    title: '用户管理',
    icon: 'el-icon-user-solid'
  },
  SESSION : {
    title: '会话管理',
    icon: 'el-icon-chat-dot-round'
  },
  SESSION_ENTITY : {
    title: '会话实体',
    icon: 'el-icon-chat-dot-round'
  },
  MESSAGE : {
    title: '消息管理',
    icon: 'el-icon-message'
  },
  MULTIMODAL : {
    title: '多模态管理',
    icon: 'el-icon-picture-outline'
  },
  IMAGE_ANALYZE : {
    title: '图像分析',
    icon: 'el-icon-picture'
  },
  TEXT_ANALYZE : {
    title: '文本分析',
    icon: 'el-icon-document'
  },
  TEXT_IMAGE : {
    title: '文生图管理',
    icon: 'el-icon-picture-outline-round'
  },
  LINK : {
    title: '链接管理',
    icon: 'el-icon-link'
  },
  IMAGE : {
    title: '图片管理',
    icon: 'el-icon-picture'
  },
  EXPORT : {
    title: '导出管理',
    icon: 'el-icon-download'
  },
  BEAUTY_HTML : {
    title: '会话HTML美化',
    icon: 'el-icon-magic-stick'
  },
  CHAT : {
    title: '聊天管理',
    icon: 'el-icon-chat-dot-round'
  },
  ABOUT_AUTHOR : {
    title: '关于作者',
    icon: 'el-icon-info'
  }
}

// 基础路由
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/login.vue'),
    hidden: true
  },
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/404',
    component: () => import('@/views/404.vue'),
    hidden: true
  },
  {
    path: '/user_management',
    name: 'user_management',
    component: Layout,
    meta: ROUTE_META.USER,
    redirect: '/user_management/user',
    children: [
      {
        path: 'user',
        name: 'user',
        component: () => import('@/views/user_management/user.vue'),
        meta: ROUTE_META.USER
      }
    ]
  },
  {
    path: '/session_management',
    name: 'session_management',
    component: Layout,
    meta: ROUTE_META.SESSION,
    redirect: '/session_management/session',
    children: [
      {
        path: 'session',
        name: 'session',
        component: () => import('@/views/session_management/session.vue'),
        meta: ROUTE_META.SESSION_ENTITY
      },
      {
        path: 'export',
        name: 'export',
        component: () => import('@/views/session_management/export.vue'),
        meta: ROUTE_META.EXPORT
      },
      {
        path: 'beauty_html',
        name: 'beauty_html',
        component: () => import('@/views/session_management/beautify_html.vue'),
        meta: ROUTE_META.BEAUTY_HTML
      }
    ]
  },
  {
    path: '/message_management',
    name: 'message_management',
    component: Layout,
    meta: ROUTE_META.MESSAGE,
    redirect: '/message_management/message',
    children: [
      {
        path: 'message',
        name: 'message',
        component: () => import('@/views/message_management/message.vue'),
        meta: ROUTE_META.MESSAGE
      }
    ]
  },
  {
    path: '/multimodal_management',
    name: 'multimodal_management',
    component: Layout,
    meta: ROUTE_META.MULTIMODAL,
    redirect: '/multimodal_management/imageAnalyze',
    children: [
      {
        path: 'imageAnalyze',
        name: 'imageAnalyze',
        component: () => import('@/views/multimodal_management/imageAnalyze.vue'),
        meta: ROUTE_META.IMAGE_ANALYZE
      },
      {
        path: 'textAnalyze',
        name: 'textAnalyze',
        component: () => import('@/views/multimodal_management/textAnalyze.vue'),
        meta: ROUTE_META.TEXT_ANALYZE
      }
    ]
  },
  {
    path: '/text_image_management',
    name: 'text_image_management',
    component: Layout,
    meta: ROUTE_META.TEXT_IMAGE,
    redirect: '/text_image_management/textImage',
    children: [
      {
        path: 'textImage',
        name: 'textImage',
        component: () => import('@/views/text_image_management/text_image.vue'),
        meta: ROUTE_META.TEXT_IMAGE
      }
    ]
  },
  {
    path: '/link_management',
    name: 'link_management',
    component: Layout,
    meta: ROUTE_META.LINK,
    redirect: '/link_management/link',
    children: [
      {
        path: 'link',
        name: 'link',
        component: () => import('@/views/link_management/link.vue'),
        meta: ROUTE_META.LINK
      }
    ]
  },
  {
    path: '/image_management',
    name: 'image_management',
    component: Layout,
    meta: ROUTE_META.IMAGE,
    redirect: '/image_management/image',
    children: [
      {
        path: 'image',
        name: 'image',
        component: () => import('@/views/image_management/image.vue'),
        meta: ROUTE_META.IMAGE
      }
    ]
  },
  {
    path: '/chat_management',
    name: 'chat_management',
    component: Layout,
    meta: ROUTE_META.CHAT,
    redirect: '/chat_management/chat',
    children: [
      {
        path: 'chat',
        name: 'chat',
        component: () => import('@/views/chat_management/chat.vue'),
        meta: ROUTE_META.CHAT
      }
    ]
  },
  {
    path: '/about_author',
    name: 'about_author',
    component: Layout,
    meta: ROUTE_META.ABOUT_AUTHOR,
    redirect: '/about_author/author',
    children: [
      {
        path: 'author',
        name: 'author',
        component: () => import('@/views/about_author/author.vue'),
        meta: ROUTE_META.ABOUT_AUTHOR
      }
    ]
  },
  // 404 页面必须放在最后
  { path: '*', redirect: '/404', hidden: true }
]

// 创建路由实例
const createRouter = () => new Router({
  mode: 'hash', // 改为 hash 模式
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

// 重置路由方法
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher
}

export default router  



