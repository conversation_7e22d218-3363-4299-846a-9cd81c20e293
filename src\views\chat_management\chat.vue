<template>
  <div class="chat-container">
    <!-- 头部信息 -->
    <div class="chat-header">
      <div class="header-left">
        <h2>ollama</h2>
        <div class="model-info">
          <span class="model-label">当前模型：</span>
          <el-tag type="success" size="small">{{ currentModel || '加载中...' }}</el-tag>
          <el-button
            type="text"
            icon="el-icon-refresh"
            size="mini"
            @click="refreshModel"
            :loading="modelLoading"
          >
            刷新
          </el-button>
        </div>
      </div>
      <div class="header-right">
        <!-- 头部右侧可以放其他功能按钮 -->
      </div>
    </div>

    <div class="chat-main">
      <!-- 左侧会话列表 -->
      <div class="session-sidebar">
        <div class="sidebar-header">
          <div class="sidebar-title">
            <h3>会话列表</h3>
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="small"
              @click="createNewSession"
            >
              新建会话
            </el-button>
          </div>
          <el-input
            v-model="sessionSearch"
            placeholder="search"
            size="small"
            clearable
            prefix-icon="el-icon-search"
          />
        </div>
        <div class="session-list">
          <div
            v-for="session in filteredSessions"
            :key="session.sessionId"
            :class="['session-item', { active: currentSessionId === session.sessionId }]"
            @click="selectSession(session.sessionId)"
          >
            <div class="session-title">
              {{ session.sessionName || '未命名会话' }}
            </div>
            <div class="session-time">
              {{ formatTime(session.createTime) }}
            </div>
            <div class="session-actions">
              <el-button
                type="text"
                icon="el-icon-delete"
                size="mini"
                @click.stop="deleteSession(session.sessionId)"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧聊天区域 -->
      <div class="chat-area">
        <!-- 聊天消息区域 -->
        <div class="messages-container" ref="messagesContainer">
          <div v-if="!currentSessionId" class="welcome-message">
            <div class="welcome-content">
              <i class="el-icon-chat-dot-round"></i>
              <h3>欢迎使用ollama</h3>
              <p>选择一个会话开始聊天，或创建新的会话，直接开始对话会自动创建一个新的会话</p>
            </div>
          </div>

          <div v-else class="messages-list" :key="`messages-${currentSessionId}-${messagesRenderKey}`">
            <div
              v-for="(message, index) in currentMessages"
              :key="`${currentSessionId}-${index}-${message.timestamp}`"
              :class="['message-item', message.role]"
              :title="`Role: ${message.role}`"
              :style="getMessageItemStyle(message.role)"
            >
              <div class="message-wrapper">
                <div class="message-avatar">
                  <img
                    v-if="message.role === 'user'"
                    :src="getUserAvatarUrl()"
                    :alt="userInfo.userName"
                    class="avatar-image"
                    @error="handleAvatarError"
                  />
                  <img
                    v-else
                    src="/avatar/ai.png"
                    alt="AI Assistant"
                    class="avatar-image"
                    @error="handleAIAvatarError"
                  />
                </div>
                <div class="message-content">
                  <div
                    class="message-text markdown-content"
                    :key="`content-${currentSessionId}-${index}-${messagesRenderKey}`"
                    v-html="formatMessage(message.content)"
                  ></div>
                  <div class="message-time">{{ formatTime(message.timestamp) }}</div>
                </div>
              </div>
            </div>

            <!-- 流式输出中的消息 -->
            <div v-if="streamingMessage" class="message-item assistant streaming" :style="getMessageItemStyle('assistant')">
              <div class="message-wrapper">
                <div class="message-avatar">
                  <img
                    src="/avatar/ai.png"
                    alt="AI Assistant"
                    class="avatar-image"
                    @error="handleAIAvatarError"
                  />
                </div>
                <div class="message-content">
                  <div
                    class="message-text markdown-content"
                    :key="`streaming-${currentSessionId}-${messagesRenderKey}`"
                    v-html="formatMessage(streamingMessage)"
                  ></div>
                  <div class="streaming-indicator">
                    <span class="typing-dots">
                      <span></span>
                      <span></span>
                      <span></span>
                    </span>
                    正在输入...
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="input-area" v-if="currentSessionId">
          <div class="input-controls">
            <el-switch
              v-model="useStreamMode"
              active-text="STREAM"
              inactive-text="CALL"
              size="small"
            />
            <el-button
              v-if="isStreaming"
              type="danger"
              size="small"
              icon="el-icon-close"
              @click="stopStreaming"
            >
              终止流
            </el-button>
          </div>

          <div class="input-box">
            <el-input
              v-model="inputMessage"
              type="textarea"
              :rows="3"
              placeholder="输入您的消息..."
              :disabled="isLoading || isStreaming"
              @keydown.ctrl.enter="sendMessage"
              resize="none"
            />
            <div class="input-actions">
              <div class="input-tips">
                <span>Ctrl + Enter 发送</span>
              </div>
              <el-button
                type="primary"
                icon="el-icon-s-promotion"
                :loading="isLoading || isStreaming"
                :disabled="!inputMessage.trim()"
                @click="sendMessage"
              >
                {{ isStreaming ? '发送中...' : '发送' }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getModel } from '@/api/yml'
import { chatCall, chatStream, stopStream } from '@/api/chat'
import { getChatSessionByUserId, insertSession, deleteSession } from '@/api/session'
import { getMessageList } from '@/api/message'
import { getInfo } from '@/api/user'
import { Message, MessageBox } from 'element-ui'
import { parseMarkdown } from '@/utils/markdown'

export default {
  name: 'ChatManagement',
  data() {
    return {
      // 模型相关
      currentModel: '',
      modelLoading: false,

      // 会话相关
      sessions: [],
      currentSessionId: '',
      sessionSearch: '',

      // 消息相关
      currentMessages: [],
      inputMessage: '',
      streamingMessage: '',
      messagesRenderKey: 0, // 用于强制重新渲染消息列表

      // 状态控制
      isLoading: false,
      isStreaming: false,
      useStreamMode: true,

      // 流式输出相关
      currentEventSource: null,
      currentStreamId: '',

      // 消息存储（按会话ID分组）
      messagesMap: new Map(),

      // 用户信息
      userInfo: {
        userName: '',
        avatar: ''
      }
    }
  },
  computed: {
    // 过滤后的会话列表
    filteredSessions() {
      if (!this.sessionSearch.trim()) {
        return this.sessions
      }
      return this.sessions.filter(session =>
        (session.sessionName && session.sessionName.toLowerCase().includes(this.sessionSearch.toLowerCase())) ||
        session.sessionId.toLowerCase().includes(this.sessionSearch.toLowerCase())
      )
    }
  },
  watch: {
    // 监听消息变化，确保样式正确应用
    currentMessages: {
      handler() {
        this.$nextTick(() => {
          // 延迟一点时间确保 DOM 更新完成
          setTimeout(() => {
            this.$forceUpdate()
            this.applyMessageStyles()
          }, 50)
        })
      },
      deep: true
    }
  },
  created() {
    this.initializeChat()
    this.loadUserInfo()
  },
  mounted() {
    // 组件挂载后立即应用样式
    this.applyMessageStyles()
  },
  beforeDestroy() {
    this.cleanup()
  },
  methods: {
    // 初始化聊天
    async initializeChat() {
      await this.loadModel()
      await this.loadSessions()
    },

    // 加载当前模型
    async loadModel() {
      this.modelLoading = true
      try {
        const response = await getModel()
        if (response.code === 20000) {
          this.currentModel = response.data
        } else {
          Message({
            message: '获取模型信息失败',
            type: 'warning'
          })
        }
      } catch (error) {
        console.error('获取模型失败:', error)
        Message({
          message: '获取模型信息失败',
          type: 'error'
        })
      } finally {
        this.modelLoading = false
      }
    },

    // 刷新模型
    refreshModel() {
      this.loadModel()
    },

    // 获取用户信息
    async loadUserInfo() {
      try {
        const response = await getInfo()
        if (response.code === 20000) {
          this.userInfo = {
            userName: response.data.userName || 'Admin',
            avatar: response.data.avatar || ''
          }
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    },

    // 获取头像URL
    getUserAvatarUrl() {
      if (!this.userInfo.avatar) {
        return 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif'
      }
      // 如果是完整URL，直接返回
      if (this.userInfo.avatar.startsWith('http')) {
        return this.userInfo.avatar
      }
      // 如果是相对路径，拼接基础URL
      return `${process.env.VUE_APP_BASE_API}${this.userInfo.avatar}`
    },

    // 头像加载错误处理
    handleAvatarError(event) {
      event.target.src = 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif'
    },

    // AI头像加载错误处理
    handleAIAvatarError(event) {
      // 如果AI头像加载失败，显示默认图标
      const avatarDiv = event.target.parentElement
      event.target.style.display = 'none'
      avatarDiv.innerHTML = '<i class="el-icon-cpu"></i>'
    },

    // 加载会话列表
    async loadSessions() {
      console.log('loadSessions被调用，当前会话ID:', this.currentSessionId) // 调试日志
      try {
        const response = await getChatSessionByUserId()
        if (response.code === 20000) {
          this.sessions = response.data || []
          // 如果有会话且没有选中的会话，选中第一个
          if (this.sessions.length > 0 && !this.currentSessionId) {
            console.log('自动选择第一个会话:', this.sessions[0].sessionId) // 调试日志
            this.selectSession(this.sessions[0].sessionId)
          }
        } else {
          Message({
            message: response.message || '获取会话列表失败',
            type: 'warning'
          })
        }
      } catch (error) {
        console.error('获取会话列表失败:', error)
        Message({
          message: '获取会话列表失败',
          type: 'error'
        })
      }
    },

    // 创建新会话
    async createNewSession() {
      try {
        const response = await insertSession('')
        if (response.code === 20000) {
          await this.loadSessions()
          Message({
            message: '新会话创建成功',
            type: 'success'
          })
          // 选中新创建的会话（通常是第一个）
          if (this.sessions.length > 0) {
            this.selectSession(this.sessions[0].sessionId)
          }
        } else {
          Message({
            message: response.message || '创建会话失败',
            type: 'error'
          })
        }
      } catch (error) {
        console.error('创建会话失败:', error)
        Message({
          message: '创建会话失败',
          type: 'error'
        })
      }
    },

    // 选择会话
    selectSession(sessionId) {
      console.log('selectSession被调用，sessionId:', sessionId, '当前会话ID:', this.currentSessionId) // 调试日志
      this.currentSessionId = sessionId
      this.messagesRenderKey = 0 // 重置渲染 key
      this.loadMessages(sessionId)
    },

    // 加载会话消息
    async loadMessages(sessionId) {
      console.log('loadMessages被调用，sessionId:', sessionId) // 调试日志
      // 先检查本地缓存
      if (this.messagesMap.has(sessionId)) {
        console.log('从缓存加载消息:', this.messagesMap.get(sessionId)) // 调试日志
        // 创建数组的副本，避免引用问题
        this.currentMessages = [...this.messagesMap.get(sessionId)]
        this.forceUpdateMessages()
        return
      }

      // 从服务器获取消息
      try {
        const response = await getMessageList(sessionId)
        if (response.code === 20000) {
          const messages = response.data || []
          // 转换消息格式
          console.log('服务器返回的原始消息:', messages) // 调试日志
          const formattedMessages = messages.map(msg => {
            // 根据sender字段判断消息类型
            let role = 'assistant' // 默认为assistant
            if (msg.role) {
              role = msg.role
            } else if (msg.isUser) {
              role = 'user'
            } else if (msg.sender && (msg.sender.includes('user') || msg.sender.includes('USER'))) {
              role = 'user'
            }

            const formattedMsg = {
              role: role,
              content: msg.content || msg.message,
              timestamp: new Date(msg.createTime || msg.timestamp)
            }
            console.log('原始消息:', msg, '格式化后:', formattedMsg) // 调试日志
            return formattedMsg
          })

          console.log('设置currentMessages为:', formattedMessages) // 调试日志
          this.currentMessages = formattedMessages
          this.messagesMap.set(sessionId, formattedMessages)
        } else {
          console.warn('获取消息失败:', response.message)
          this.currentMessages = []
          this.messagesMap.set(sessionId, [])
        }
      } catch (error) {
        console.error('获取消息失败:', error)
        this.currentMessages = []
        this.messagesMap.set(sessionId, [])
        Message({
          message: '获取消息历史失败',
          type: 'warning'
        })
      }

      this.forceUpdateMessages()
    },

    // 删除会话
    deleteSession(sessionId) {
      MessageBox.confirm(
        '确定要删除这个会话吗？删除后无法恢复。',
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          const response = await deleteSession(sessionId)
          if (response.code === 20000) {
            // 从本地缓存中删除消息
            this.messagesMap.delete(sessionId)
            // 如果删除的是当前会话，清空当前会话
            if (this.currentSessionId === sessionId) {
              this.currentSessionId = ''
              this.currentMessages = []
            }
            // 重新加载会话列表
            await this.loadSessions()
            Message({
              message: '会话删除成功',
              type: 'success'
            })
          } else {
            Message({
              message: response.message || '删除会话失败',
              type: 'error'
            })
          }
        } catch (error) {
          console.error('删除会话失败:', error)
          Message({
            message: '删除会话失败',
            type: 'error'
          })
        }
      }).catch(() => {
        Message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },

    // 发送消息
    async sendMessage() {
      if (!this.inputMessage.trim()) {
        return
      }

      const message = this.inputMessage.trim()
      this.inputMessage = ''

      // 立即显示用户消息
      this.addMessage('user', message)
      this.scrollToBottom()

      if (this.useStreamMode) {
        await this.sendStreamMessage(message)
      } else {
        await this.sendNormalMessage(message)
      }
    },

    // 普通模式发送消息
    async sendNormalMessage(message) {
      this.isLoading = true
      try {
        const response = await chatCall(message, this.currentSessionId)
        if (response.code === 20000) {
          // 添加AI回复消息
          if (response.data && response.data.content) {
            this.addMessage('assistant', response.data.content)
          }
          // 刷新会话列表以更新可能变化的会话标题
          await this.loadSessions()
        } else {
          Message({
            message: response.message || '发送消息失败',
            type: 'error'
          })
          // 发送失败时，移除刚添加的用户消息
          if (this.currentMessages.length > 0 &&
              this.currentMessages[this.currentMessages.length - 1].role === 'user') {
            this.currentMessages.pop()
          }
        }
      } catch (error) {
        console.error('发送消息失败:', error)
        Message({
          message: '发送消息失败',
          type: 'error'
        })
        // 发送失败时，移除刚添加的用户消息
        if (this.currentMessages.length > 0 &&
            this.currentMessages[this.currentMessages.length - 1].role === 'user') {
          this.currentMessages.pop()
        }
      } finally {
        this.isLoading = false
        this.scrollToBottom()
      }
    },

    // 流式模式发送消息
    async sendStreamMessage(message) {
      this.isStreaming = true
      this.streamingMessage = ''
      this.currentStreamId = this.generateStreamId()

      try {
        this.currentEventSource = chatStream(message, this.currentSessionId, this.currentStreamId)

        this.currentEventSource.onmessage = (event) => {
          const data = event.data
          console.log('收到流式数据:', data) // 调试日志

          if (data === '[DONE]') {
            this.finishStreaming()
          } else if (data && data.trim()) {
            // 确保数据不为空且去除首尾空白
            this.streamingMessage += data.trim()
            this.forceUpdateMessages()
          }
        }

        this.currentEventSource.onerror = (error) => {
          console.error('流式输出错误:', error)
          this.finishStreaming()
          Message({
            message: '流式输出发生错误',
            type: 'error'
          })
        }

      } catch (error) {
        console.error('启动流式输出失败:', error)
        this.isStreaming = false
        Message({
          message: '启动流式输出失败',
          type: 'error'
        })
      }
    },

    // 完成流式输出
    async finishStreaming() {
      console.log('finishStreaming被调用，当前流式消息:', this.streamingMessage) // 调试日志

      if (this.streamingMessage && this.streamingMessage.trim()) {
        console.log('finishStreaming: 添加AI消息到列表') // 调试日志
        // 将流式消息添加到消息列表
        this.addMessage('assistant', this.streamingMessage)
        // 刷新会话列表以更新可能变化的会话标题
        await this.loadSessions()
      }

      // 清理状态
      this.streamingMessage = ''
      this.isStreaming = false
      this.currentStreamId = null

      // 关闭事件源（如果还没关闭）
      if (this.currentEventSource) {
        this.currentEventSource.close()
        this.currentEventSource = null
      }

      // 强制重新渲染以确保样式正确应用
      this.forceUpdateMessages()
    },

    // 停止流式输出
    async stopStreaming() {
      console.log('stopStreaming被调用') // 调试日志

      // 先移除事件处理器，防止关闭时触发onerror
      if (this.currentEventSource) {
        this.currentEventSource.onmessage = null
        this.currentEventSource.onerror = null
        this.currentEventSource.close()
        this.currentEventSource = null
      }

      if (this.currentStreamId) {
        try {
          await stopStream(this.currentStreamId)
        } catch (error) {
          console.error('停止流式输出失败:', error)
        }
      }

      // 手动完成流式输出
      this.finishStreaming()
    },

    // 添加消息到当前会话
    addMessage(role, content) {
      const message = {
        role: role,
        content: content,
        timestamp: new Date()
      }

      console.log('添加消息:', message) // 调试日志
      console.log('调用栈:', new Error().stack) // 调试：查看调用栈
      this.currentMessages.push(message)

      // 更新消息缓存 - 重新创建缓存数组
      this.messagesMap.set(this.currentSessionId, [...this.currentMessages])

      // 强制重新渲染以确保样式正确应用
      this.forceUpdateMessages()
    },

    // 生成流式输出ID
    generateStreamId() {
      return 'stream_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
    },

    // 滚动到底部
    scrollToBottom() {
      const container = this.$refs.messagesContainer
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    },

    // 格式化消息内容
    formatMessage(content) {
      if (!content) return ''

      // 使用 markdown 解析器处理内容
      return parseMarkdown(content)
    },

    // 强制更新消息渲染
    forceUpdateMessages() {
      // 增加渲染 key 强制重新渲染整个消息列表
      this.messagesRenderKey++

      // 使用多重强制更新确保样式正确应用
      this.$nextTick(() => {
        this.$forceUpdate()
        // 手动应用样式
        this.applyMessageStyles()
        // 再次强制更新以确保样式完全应用
        setTimeout(() => {
          this.$forceUpdate()
          this.applyMessageStyles()
          this.$nextTick(() => {
            this.scrollToBottom()
          })
        }, 100)
      })
    },

    // 手动应用消息样式
    applyMessageStyles() {
      this.$nextTick(() => {
        const messageItems = this.$el.querySelectorAll('.message-item')
        messageItems.forEach(item => {
          // 确保基本布局样式
          item.style.display = 'flex'
          item.style.marginBottom = '16px'
          item.style.alignItems = 'flex-start'

          if (item.classList.contains('user')) {
            item.style.justifyContent = 'flex-end'
          } else if (item.classList.contains('assistant')) {
            item.style.justifyContent = 'flex-start'
          }

          // 确保消息文本样式
          const messageText = item.querySelector('.message-text')
          if (messageText) {
            messageText.style.background = item.classList.contains('user') ? '#e4e7ed' : '#f0f0f0'
            messageText.style.padding = '12px 16px'
            messageText.style.borderRadius = '12px'
            messageText.style.fontSize = '14px'
            messageText.style.lineHeight = '1.5'
            messageText.style.wordWrap = 'break-word'
            messageText.style.overflowWrap = 'break-word'
          }
        })
      })
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      const now = new Date()
      const diff = now - date

      // 如果是今天
      if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
        return date.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        })
      }

      // 如果是昨天
      if (diff < 48 * 60 * 60 * 1000) {
        return '昨天 ' + date.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        })
      }

      // 其他情况显示日期
      return date.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    // 清理资源
    cleanup() {
      if (this.currentEventSource) {
        this.currentEventSource.close()
      }
    },

    // 调试方法：手动触发样式重新渲染
    debugForceRender() {
      console.log('手动触发样式重新渲染')
      this.forceUpdateMessages()
    },

    // 获取消息项的内联样式
    getMessageItemStyle(role) {
      const baseStyle = {
        display: 'flex',
        marginBottom: '16px',
        alignItems: 'flex-start'
      }

      if (role === 'user') {
        return {
          ...baseStyle,
          justifyContent: 'flex-end'
        }
      } else {
        return {
          ...baseStyle,
          justifyContent: 'flex-start'
        }
      }
    }
  }
}
</script>

<style scoped >
.chat-container {
  height: 100%; /* 使用AppMain提供的高度 */
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  background: #f5f5f5;
  overflow: hidden;
}

.chat-header {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-align: center;
  align-items: center;
  padding: 15px 20px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  -ms-flex-negative: 0;
  flex-shrink: 0; /* 防止头部被压缩 */
  height: 70px; /* 减少头部高度 */
}

.header-left h2 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.model-info {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
}

.model-info > * + * {
  margin-left: 8px;
}

.model-label {
  color: #606266;
  font-size: 14px;
}

.chat-main {
  display: -ms-flexbox;
  display: flex;
  -ms-flex: 1;
  flex: 1;
  overflow: hidden;
}

.session-sidebar {
  width: 300px;
  background: white;
  border-right: 1px solid #e4e7ed;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.sidebar-title {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 15px;
}

.sidebar-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.session-list {
  -ms-flex: 1;
  flex: 1;
  overflow-y: auto;
}

.session-item {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
  position: relative;
}

.session-item:hover {
  background: #f5f7fa;
}

.session-item.active {
  background: #ecf5ff;
  border-right: 3px solid #409eff;
}

.session-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 5px;
  font-weight: 500;
}

.session-time {
  font-size: 12px;
  color: #909399;
}

.session-actions {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.3s;
}

.session-item:hover .session-actions {
  opacity: 1;
}

.chat-area {
  -ms-flex: 1;
  flex: 1;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  background: white;
  height: 100%;
  overflow: hidden;
}

.messages-container {
  -ms-flex: 1;
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  min-height: 0; /* 确保flex子元素可以收缩 */
}

.welcome-message {
  height: 100%;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.welcome-content {
  text-align: center;
  color: #909399;
}

.welcome-content i {
  font-size: 48px;
  margin-bottom: 20px;
  color: #c0c4cc;
}

.welcome-content h3 {
  margin: 0 0 10px 0;
  font-size: 18px;
  color: #606266;
}

.welcome-content p {
  margin: 0;
  font-size: 14px;
}

.messages-list {
  width: 100%;
  padding: 0;
  display: flex !important;
  flex-direction: column !important;
}

.message-item {
  display: flex !important;
  margin-bottom: 20px;
  width: 100% !important;
}

/* 用户消息 - 右对齐 */
.message-item.user {
  justify-content: flex-end !important;
}

.message-item.user .message-wrapper {
  display: flex !important;
  flex-direction: row-reverse !important;
  align-items: flex-start !important;
  max-width: 80% !important;
  gap: 12px !important;
}

/* AI消息 - 左对齐 */
.message-item.assistant {
  justify-content: flex-start !important;
}

.message-item.assistant .message-wrapper {
  display: flex !important;
  align-items: flex-start !important;
  max-width: 80% !important;
  gap: 12px !important;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f0f0f0;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  overflow: hidden;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.message-item.user .message-avatar {
  background: #409eff;
  color: white;
}

.message-item.assistant .message-avatar {
  background: transparent;
  color: white;
}

/* 当AI头像加载失败时显示背景色 */
.message-item.assistant .message-avatar i {
  background: #67c23a;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.message-content {
  flex: 1;
  min-width: 200px;
}

/* 用户消息内容样式 */
.message-item.user .message-content {
  flex: none !important;
  min-width: auto !important;
  width: auto !important;
}

.message-text {
  background: #f0f0f0;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 14px;
  line-height: 1.5;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.message-item.user .message-text {
  background: #e4e7ed !important;
  color: #303133 !important;
  text-align: right !important;
  display: inline-block !important;
  width: auto !important;
  max-width: none !important;
}

.message-item.assistant .message-text {
  background: #f0f0f0;
  color: #303133;
}

.message-time {
  font-size: 12px;
  color: #c0c4cc;
  margin-top: 5px;
}

.streaming-indicator {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.streaming-indicator > * + * {
  margin-left: 8px;
}

.typing-dots {
  display: -ms-flexbox;
  display: flex;
}

.typing-dots > * + * {
  margin-left: 3px;
}

.typing-dots span {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: #909399;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.input-area {
  border-top: 1px solid #e4e7ed;
  padding: 20px;
  background: #fafafa;
  -ms-flex-negative: 0;
  flex-shrink: 0; /* 防止输入区域被压缩 */
  height: 200px; /* 使用固定高度而不是最小高度 */
}

.input-controls {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 15px;
}

.input-box {
  position: relative;
}

.input-actions {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-align: center;
  align-items: center;
  margin-top: 15px;
  padding-bottom: 5px; /* 确保底部有足够空间 */
}

.input-tips {
  font-size: 12px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-main {
    -ms-flex-direction: column;
    flex-direction: column;
  }

  .session-sidebar {
    width: 100%;
    height: 200px;
  }

  .session-list {
    display: -ms-flexbox;
    display: flex;
    overflow-x: auto;
    overflow-y: hidden;
  }

  .session-item {
    min-width: 150px;
    border-right: 1px solid #f0f0f0;
    border-bottom: none;
  }

  .message-content {
    max-width: 85%;
  }
}

/* 滚动条样式 */
.session-list::-webkit-scrollbar,
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.session-list::-webkit-scrollbar-track,
.messages-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.session-list::-webkit-scrollbar-thumb,
.messages-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.session-list::-webkit-scrollbar-thumb:hover,
.messages-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 输入框样式优化 */
.el-textarea__inner {
  border-radius: 8px;
  border: 1px solid #dcdfe6;
  transition: border-color 0.3s;
}

.el-textarea__inner:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 按钮样式优化 */
.input-actions .el-button--primary {
  border-radius: 6px;
  padding: 8px 20px;
}

/* 流式输出动画 */
.streaming .message-text {
  position: relative;
}

.streaming .message-text::after {
  content: '';
  position: absolute;
  right: 8px;
  bottom: 8px;
  width: 8px;
  height: 8px;
  background: #409eff;
  border-radius: 50%;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

</style>

<!-- Markdown 样式 - 非 scoped 以确保正确应用到动态内容 -->
<style>
/* Markdown 样式 */
.markdown-content {
  line-height: 1.6 !important;
  color: #333 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif !important;
}

/* 标题样式 */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin: 16px 0 8px 0 !important;
  font-weight: 600 !important;
  line-height: 1.25 !important;
  color: #1f2328 !important;
}

.markdown-content h1 {
  font-size: 2em !important;
  border-bottom: 1px solid #d1d9e0 !important;
  padding-bottom: 8px !important;
}

.markdown-content h2 {
  font-size: 1.5em !important;
  border-bottom: 1px solid #d1d9e0 !important;
  padding-bottom: 8px !important;
}

.markdown-content h3 {
  font-size: 1.25em !important;
}

.markdown-content h4 {
  font-size: 1em !important;
}

.markdown-content h5 {
  font-size: 0.875em !important;
}

.markdown-content h6 {
  font-size: 0.85em !important;
  color: #656d76 !important;
}

/* 段落样式 */
.markdown-content p {
  margin: 8px 0 !important;
}

/* 列表样式 */
.markdown-content ul,
.markdown-content ol {
  margin: 8px 0 !important;
  padding-left: 24px !important;
}

.markdown-content li {
  margin: 4px 0 !important;
}

.markdown-content ul li {
  list-style-type: disc !important;
}

.markdown-content ol li {
  list-style-type: decimal !important;
}

/* 嵌套列表 */
.markdown-content ul ul,
.markdown-content ol ol,
.markdown-content ul ol,
.markdown-content ol ul {
  margin: 0 !important;
}

/* 代码样式 */
.markdown-content code {
  background-color: #f6f8fa !important;
  border-radius: 3px !important;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace !important;
  font-size: 85% !important;
  padding: 2px 4px !important;
  color: #d73a49 !important;
}

.markdown-content pre {
  background-color: #f6f8fa !important;
  border-radius: 6px !important;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace !important;
  font-size: 85% !important;
  line-height: 1.45 !important;
  margin: 16px 0 !important;
  overflow: auto !important;
  padding: 16px !important;
}

.markdown-content pre code {
  background-color: transparent !important;
  border-radius: 0 !important;
  color: #1f2328 !important;
  font-size: 100% !important;
  padding: 0 !important;
}

/* 代码块语言标识 */
.markdown-content .code-header {
  background-color: #f1f3f4 !important;
  border-radius: 6px 6px 0 0 !important;
  border-bottom: 1px solid #d1d9e0 !important;
  color: #656d76 !important;
  font-size: 12px !important;
  padding: 8px 16px !important;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace !important;
}

.markdown-content .code-block-with-header {
  margin: 16px 0 !important;
  border-radius: 6px !important;
  border: 1px solid #d1d9e0 !important;
  overflow: hidden !important;
}

.markdown-content .code-block-with-header pre {
  margin: 0 !important;
  border-radius: 0 !important;
  border: none !important;
}

/* 引用样式 */
.markdown-content blockquote {
  border-left: 4px solid #d1d9e0 !important;
  color: #656d76 !important;
  margin: 16px 0 !important;
  padding: 0 16px !important;
}

.markdown-content blockquote p {
  margin: 8px 0 !important;
}

/* 链接样式 */
.markdown-content a {
  color: #0969da !important;
  text-decoration: none !important;
}

.markdown-content a:hover {
  text-decoration: underline !important;
}

/* 表格样式 */
.markdown-content table {
  border-collapse: collapse !important;
  border-spacing: 0 !important;
  margin: 16px 0 !important;
  width: 100% !important;
}

.markdown-content table th,
.markdown-content table td {
  border: 1px solid #d1d9e0 !important;
  padding: 8px 12px !important;
  text-align: left !important;
}

.markdown-content table th {
  background-color: #f6f8fa !important;
  font-weight: 600 !important;
}

.markdown-content table tr:nth-child(even) {
  background-color: #f6f8fa !important;
}

/* 分割线样式 */
.markdown-content hr {
  background-color: #d1d9e0 !important;
  border: 0 !important;
  height: 1px !important;
  margin: 24px 0 !important;
}

/* 强调样式 */
.markdown-content strong,
.markdown-content b {
  font-weight: 600 !important;
}

.markdown-content em,
.markdown-content i {
  font-style: italic !important;
}

/* 删除线样式 */
.markdown-content del,
.markdown-content s {
  text-decoration: line-through !important;
}

/* 消息布局样式 - 确保在动态渲染时正确应用 */
.message-item {
  display: flex !important;
  margin-bottom: 16px !important;
  align-items: flex-start !important;
}

.message-item.user {
  justify-content: flex-end !important;
}

.message-item.assistant {
  justify-content: flex-start !important;
}

.message-wrapper {
  display: flex !important;
  align-items: flex-start !important;
  max-width: 70% !important;
}

.message-item.user .message-wrapper {
  flex-direction: row-reverse !important;
}

.message-avatar {
  width: 32px !important;
  height: 32px !important;
  border-radius: 50% !important;
  overflow: hidden !important;
  margin: 0 8px !important;
  flex-shrink: 0 !important;
}

.message-avatar img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

.message-content {
  flex: 1 !important;
  min-width: 0 !important;
}

.message-text {
  background: #f0f0f0 !important;
  padding: 12px 16px !important;
  border-radius: 12px !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
}

.message-item.user .message-text {
  background: #e4e7ed !important;
  color: #303133 !important;
  text-align: right !important;
  display: inline-block !important;
  width: auto !important;
  max-width: none !important;
}

.message-item.assistant .message-text {
  background: #f0f0f0 !important;
  color: #303133 !important;
}

.message-time {
  font-size: 12px !important;
  color: #c0c4cc !important;
  margin-top: 5px !important;
}

/* 流式输出样式 */
.streaming .message-text {
  position: relative !important;
}

.streaming .message-text::after {
  content: '' !important;
  position: absolute !important;
  right: 8px !important;
  bottom: 8px !important;
  width: 8px !important;
  height: 8px !important;
  background: #409eff !important;
  border-radius: 50% !important;
  animation: pulse 1s infinite !important;
}

.streaming-indicator {
  display: flex !important;
  align-items: center !important;
  margin-top: 8px !important;
  color: #909399 !important;
  font-size: 12px !important;
}

.typing-dots {
  display: inline-flex !important;
  margin-right: 8px !important;
}

.typing-dots span {
  width: 4px !important;
  height: 4px !important;
  border-radius: 50% !important;
  background-color: #909399 !important;
  margin: 0 1px !important;
  animation: typing 1.4s infinite ease-in-out !important;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s !important;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s !important;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0) !important;
  }
  40% {
    transform: scale(1) !important;
  }
}

@keyframes pulse {
  0% {
    opacity: 1 !important;
    transform: scale(1) !important;
  }
  50% {
    opacity: 0.5 !important;
    transform: scale(1.1) !important;
  }
  100% {
    opacity: 1 !important;
    transform: scale(1) !important;
  }
}
</style>