import request from '@/utils/request'


// 获取用户信息
export function getInfo() {
  return request({
    url: '/user/getUserInfo',
    method: 'get'
  })
}

// 用户退出 - 只使token失效
export function quit() {
  return request({
    url: '/user/quit',
    method: 'post'
  })
}

// 用户注销 - 使token失效并清除用户数据
export function logout() {
  return request({
    url: '/user/logout',
    method: 'delete'
  })
}


// 获取用户列表
export function getUserList(params) {
  return request({
    url: '/user/getUserList',
    method: 'get',
    params
  })
}

// 更新用户
export function updateUser(data) {
  return request({
    url: '/user/updateUser',
    method: 'post',
    data
  })
}

// 删除用户
export function deleteUser(userId) {
  return request({
    url: `/user/deleteUser/${userId}`,
    method: 'delete'
  })
}



