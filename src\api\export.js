import request from '@/utils/request'

// 导出会话标题为txt
export function exportSessionNameTxt() {
  return request({
    url: '/export/exportSessionNameTxt',
    method: 'get'
  })
}

// 导出会话为html
export function exportSessionHtml(sessionId) {
  return request({
    url: `/export/exportSessionHtml/${sessionId}`,
    method: 'get'
  })
}

// 导出会话为xml
export function exportSessionXml(sessionId, category) {
  return request({
    url: `/export/exportSessionXml/${sessionId}`,
    method: 'get',
    params: {
      category: category
    }
  })
}

// 导出会话为excel
export function exportSessionExcel(sessionId, category) {
  return request({
    url: `/export/exportSessionExcel/${sessionId}`,
    method: 'get',
    params: {
      category: category
    }
  })
}

// 导出会话为csv
export function exportSessionCsv(sessionId, category) {
  return request({
    url: `/export/exportSessionCsv/${sessionId}`,
    method: 'get',
    params: {
      category: category
    }
  })
}