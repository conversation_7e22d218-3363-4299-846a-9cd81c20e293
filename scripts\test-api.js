const axios = require('axios');
const { apiTests } = require('../tests/api-integration-test.js');

// 配置axios实例
const api = axios.create({
  baseURL: 'http://localhost:8080',
  timeout: 5000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 测试结果统计
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

/**
 * 运行单个API测试
 */
async function runSingleTest(test) {
  console.log(`\n🧪 测试: ${test.name}`);
  console.log(`📡 ${test.method} ${test.url}`);
  
  try {
    const config = {
      method: test.method.toLowerCase(),
      url: test.url
    };
    
    // 添加请求参数
    if (test.data) {
      config.data = test.data;
    }
    
    if (test.params) {
      config.params = test.params;
    }
    
    if (test.headers) {
      config.headers = { ...config.headers, ...test.headers };
    }
    
    const response = await api(config);
    
    // 检查响应格式
    const isValidResponse = response.data && 
                           typeof response.data.code === 'number' &&
                           typeof response.data.msg === 'string';
    
    if (isValidResponse && response.data.code === 20000) {
      console.log('✅ 测试通过');
      console.log(`📄 响应: ${JSON.stringify(response.data, null, 2)}`);
      testResults.passed++;
    } else {
      console.log('❌ 测试失败 - 响应格式不正确');
      console.log(`📄 实际响应: ${JSON.stringify(response.data, null, 2)}`);
      console.log(`📄 期望响应: ${JSON.stringify(test.expectedResponse, null, 2)}`);
      testResults.failed++;
      testResults.errors.push({
        test: test.name,
        error: '响应格式不正确',
        actual: response.data,
        expected: test.expectedResponse
      });
    }
    
  } catch (error) {
    console.log('❌ 测试失败 - 请求异常');
    console.log(`🚨 错误信息: ${error.message}`);
    
    if (error.response) {
      console.log(`📄 错误响应: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    
    testResults.failed++;
    testResults.errors.push({
      test: test.name,
      error: error.message,
      response: error.response?.data
    });
  }
  
  testResults.total++;
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始API接口对接测试...\n');
  console.log('📋 测试配置:');
  console.log(`   - 后端服务地址: http://localhost:8080`);
  console.log(`   - 测试接口数量: ${apiTests.length}`);
  console.log(`   - 期望成功状态码: 20000`);
  
  // 检查后端服务是否可用
  try {
    console.log('\n🔍 检查后端服务连接...');
    await api.get('/');
    console.log('✅ 后端服务连接正常');
  } catch (error) {
    console.log('❌ 无法连接到后端服务');
    console.log('🚨 请确保后端服务运行在 http://localhost:8080');
    console.log(`🚨 错误信息: ${error.message}`);
    return;
  }
  
  // 运行所有测试
  for (const test of apiTests) {
    await runSingleTest(test);
    // 添加延迟避免请求过快
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  // 输出测试结果
  console.log('\n' + '='.repeat(50));
  console.log('📊 测试结果统计');
  console.log('='.repeat(50));
  console.log(`总测试数: ${testResults.total}`);
  console.log(`通过: ${testResults.passed} ✅`);
  console.log(`失败: ${testResults.failed} ❌`);
  console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  
  if (testResults.errors.length > 0) {
    console.log('\n🚨 失败详情:');
    testResults.errors.forEach((error, index) => {
      console.log(`\n${index + 1}. ${error.test}`);
      console.log(`   错误: ${error.error}`);
      if (error.actual) {
        console.log(`   实际响应: ${JSON.stringify(error.actual, null, 2)}`);
      }
      if (error.expected) {
        console.log(`   期望响应: ${JSON.stringify(error.expected, null, 2)}`);
      }
    });
  }
  
  console.log('\n' + '='.repeat(50));
  
  if (testResults.failed === 0) {
    console.log('🎉 所有测试通过！接口对接成功！');
  } else {
    console.log('⚠️  部分测试失败，请检查接口配置和后端实现');
  }
}

// 运行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  runAllTests,
  runSingleTest
};
