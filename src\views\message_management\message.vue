<template>
  <div class="app-container">
     <div class="page-header">
      <h2>消息管理</h2>
    </div>
    <!-- 会话选择和操作区域 -->
    <div class="filter-container">
      <el-select
        v-model="selectedSessionId"
        placeholder="请选择会话"
        clearable
        class="filter-item"
        style="width: 300px"
        @change="handleSessionChange"
      >
        <el-option
          v-for="session in sessionList"
          :key="session.sessionId"
          :label="session.sessionName || '未命名会话'"
          :value="session.sessionId"
        />
      </el-select>

      <el-input
        v-model="searchKeyword"
        placeholder="搜索消息内容..."
        clearable
        class="filter-item"
        style="width: 200px; margin-left: 10px;"
      />

      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
        style="margin-left: 10px;"
      >
        搜索
      </el-button>

      <el-button
        class="filter-item"
        type="info"
        icon="el-icon-refresh"
        @click="refreshMessages"
        style="margin-left: 10px;"
      >
        刷新
      </el-button>

      <el-button
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        @click="handleDeleteAllMessages"
        style="margin-left: 10px;"
        :disabled="!selectedSessionId"
      >
        清空消息
      </el-button>
    </div>

    <!-- 消息统计信息 -->
    <div class="stats-container" v-if="selectedSessionId">
      <el-card class="stats-card">
        <div class="stats-content">
          <span class="stats-label">当前会话：</span>
          <span class="stats-value">{{ getCurrentSessionName() }}</span>
          <span class="stats-label" style="margin-left: 20px;">消息总数：</span>
          <span class="stats-value">{{ messageCount }}</span>
          <span class="stats-label" style="margin-left: 20px;">显示条数：</span>
          <span class="stats-value">{{ filteredMessages.length }}</span>
        </div>
      </el-card>
    </div>

    <!-- 消息列表 -->
    <el-table
      style="margin-top: 20px;"
      v-loading="listLoading"
      element-loading-text="Loading"
      border
      :data="paginatedMessages"
      fit
      highlight-current-row
      :row-class-name="getRowClassName"
    >
      <el-table-column label="消息ID" width="80" align="center" prop="messageId" />
      <el-table-column label="发送者" width="100" align="center" prop="sender">
        <template #default="{ row }">
          <el-tag :type="getSenderTagType(row.sender)">{{ row.sender }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="思考过程" width="220" align="left">
        <template #default="{ row }">
          <div class="think-cell">
            <el-tooltip
              :content="row.think"
              placement="top"
              :disabled="!row.think || row.think.length <= 50"
              effect="light"
              :open-delay="300"
            >
              <div class="message-think" :class="{ 'has-content': row.think }">
                {{ formatThink(row.think) }}
              </div>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="消息内容" align="left" min-width="350">
        <template #default="{ row }">
          <div class="content-cell">
            <el-tooltip
              :content="row.content"
              placement="top"
              :disabled="!row.content || row.content.length <= 120"
              effect="light"
              :open-delay="300"
            >
              <div class="message-content" :class="{ 'expandable': row.content && row.content.length > 120 }">
                {{ formatContent(row.content) }}
              </div>
            </el-tooltip>
            <div class="content-actions" v-if="row.content && row.content.length > 120">
              <el-button type="text" size="mini" @click.stop="handleViewDetail(row)">
                <i class="el-icon-view"></i> 查看全文
              </el-button>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="180">
        <template #default="{ row }">
          <span>{{ formatCreateTime(row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
        <template #default="{ row }">
          <el-button type="text" size="mini" @click="handleViewDetail(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="filteredMessages.length > 0"
      :total="filteredMessages.length"
      :page.sync="currentPage"
      :limit.sync="pageSize"
      @pagination="handlePagination"
    />

    <!-- 消息详情对话框 -->
    <el-dialog
      title="消息详情"
      :visible.sync="detailDialogVisible"
      width="75%"
      :before-close="handleCloseDetail"
      class="message-detail-dialog"
    >
      <div v-if="selectedMessage" class="message-detail">
        <!-- 基本信息卡片 -->
        <el-card class="info-card" shadow="never">
          <div slot="header" class="card-header">
            <span><i class="el-icon-info"></i> 基本信息</span>
          </div>
          <el-descriptions :column="2" border size="medium">
            <el-descriptions-item label="消息ID" label-class-name="desc-label">
              <el-tag size="mini" type="info">{{ selectedMessage.messageId }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="会话ID" label-class-name="desc-label">
              <el-tag size="mini" type="info">{{ selectedMessage.sessionId }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="发送者" label-class-name="desc-label">
              <el-tag :type="getSenderTagType(selectedMessage.sender)" size="medium">
                <i :class="getSenderIcon(selectedMessage.sender)"></i>
                {{ getSenderDisplayName(selectedMessage.sender) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间" label-class-name="desc-label">
              <span class="time-display">
                <i class="el-icon-time"></i>
                {{ formatCreateTime(selectedMessage.createTime) }}
              </span>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 思考过程卡片 -->
        <el-card class="content-card think-card" shadow="never" v-if="selectedMessage.think">
          <div slot="header" class="card-header">
            <span><i class="el-icon-cpu"></i> 思考过程</span>
            <el-tag size="mini" type="warning">AI推理</el-tag>
          </div>
          <div class="detail-content think-content">
            <pre class="content-text">{{ selectedMessage.think }}</pre>
          </div>
        </el-card>

        <!-- 消息内容卡片 -->
        <el-card class="content-card message-card" shadow="never">
          <div slot="header" class="card-header">
            <span><i class="el-icon-chat-dot-round"></i> 消息内容</span>
            <el-tag size="mini" :type="selectedMessage.sender === 'user' ? 'primary' : 'success'">
              {{ selectedMessage.sender === 'user' ? '用户消息' : 'AI回复' }}
            </el-tag>
          </div>
          <div class="detail-content message-content-detail">
            <pre class="content-text">{{ selectedMessage.content || '暂无内容' }}</pre>
          </div>
        </el-card>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCloseDetail">关闭</el-button>
        <el-button type="primary" @click="copyMessageContent">
          <i class="el-icon-document-copy"></i> 复制内容
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getMessageList, getMessageCount, deleteMessages } from '@/api/message'
import { getChatSessionByUserId } from '@/api/session'
import { Message, MessageBox } from 'element-ui'
import Pagination from '@/components/Pagination.vue'

export default {
  name: 'MessageManagement',
  components: {
    Pagination
  },
  data() {
    return {
      sessionList: [],
      selectedSessionId: '',
      messages: [],
      filteredMessages: [],
      searchKeyword: '',
      listLoading: false,
      messageCount: 0,
      currentPage: 1,
      pageSize: 20,
      detailDialogVisible: false,
      selectedMessage: null
    }
  },
  computed: {
    paginatedMessages() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredMessages.slice(start, end)
    }
  },
  created() {
    this.fetchSessionList()
  },
  methods: {
    // 获取会话列表
    async fetchSessionList() {
      try {
        const response = await getChatSessionByUserId()
        if (response.code === 20000) {
          this.sessionList = response.data || []
        } else {
          this.sessionList = []
          Message({
            message: response.message || '获取会话列表失败',
            type: 'warning'
          })
        }
      } catch (error) {
        console.error('获取会话列表失败:', error)
        Message({
          message: '获取会话列表失败',
          type: 'error'
        })
        this.sessionList = []
      }
    },

    // 会话选择变化
    async handleSessionChange(sessionId) {
      if (sessionId) {
        await this.fetchMessages(sessionId)
        await this.fetchMessageCount(sessionId)
      } else {
        this.messages = []
        this.filteredMessages = []
        this.messageCount = 0
        this.currentPage = 1
      }
    },

    // 获取消息列表
    async fetchMessages(sessionId) {
      if (!sessionId) return

      this.listLoading = true
      try {
        const response = await getMessageList(sessionId)
        if (response.code === 20000) {
          this.messages = response.data || []
          this.filteredMessages = [...this.messages]
          this.currentPage = 1
        } else {
          this.messages = []
          this.filteredMessages = []
          Message({
            message: response.message || '获取消息列表失败',
            type: 'warning'
          })
        }
      } catch (error) {
        console.error('获取消息列表失败:', error)
        Message({
          message: '获取消息列表失败',
          type: 'error'
        })
        this.messages = []
        this.filteredMessages = []
      } finally {
        this.listLoading = false
      }
    },

    // 获取消息数量
    async fetchMessageCount(sessionId) {
      if (!sessionId) return

      try {
        const response = await getMessageCount(sessionId)
        if (response.code === 20000) {
          this.messageCount = response.data || 0
        } else {
          this.messageCount = 0
        }
      } catch (error) {
        console.error('获取消息数量失败:', error)
        this.messageCount = 0
      }
    },

    // 搜索过滤
    handleFilter() {
      if (!this.searchKeyword.trim()) {
        this.filteredMessages = [...this.messages]
      } else {
        this.filteredMessages = this.messages.filter(message =>
          (message.content && message.content.toLowerCase().includes(this.searchKeyword.toLowerCase())) ||
          (message.think && message.think.toLowerCase().includes(this.searchKeyword.toLowerCase())) ||
          (message.sender && message.sender.toLowerCase().includes(this.searchKeyword.toLowerCase()))
        )
      }
      this.currentPage = 1
    },

    // 刷新消息
    async refreshMessages() {
      if (this.selectedSessionId) {
        await this.fetchMessages(this.selectedSessionId)
        await this.fetchMessageCount(this.selectedSessionId)
        Message({
          message: '刷新成功',
          type: 'success'
        })
      } else {
        Message({
          message: '请先选择会话',
          type: 'warning'
        })
      }
    },

    // 删除所有消息
    handleDeleteAllMessages() {
      if (!this.selectedSessionId) {
        Message({
          message: '请先选择会话',
          type: 'warning'
        })
        return
      }

      MessageBox.confirm(
        `确定要删除会话 "${this.getCurrentSessionName()}" 的所有消息吗？此操作不可恢复！`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          const response = await deleteMessages(this.selectedSessionId)
          if (response.code === 20000) {
            Message({
              message: response.data || '删除成功',
              type: 'success'
            })
            // 刷新消息列表
            await this.fetchMessages(this.selectedSessionId)
            await this.fetchMessageCount(this.selectedSessionId)
          } else {
            Message({
              message: response.message || '删除失败',
              type: 'error'
            })
          }
        } catch (error) {
          console.error('删除消息失败:', error)
          Message({
            message: '删除失败',
            type: 'error'
          })
        }
      }).catch(() => {
        Message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },

    // 查看消息详情
    handleViewDetail(row) {
      this.selectedMessage = row
      this.detailDialogVisible = true
    },

    // 关闭详情对话框
    handleCloseDetail() {
      this.detailDialogVisible = false
      this.selectedMessage = null
    },

    // 分页处理
    handlePagination() {
      // 分页组件会自动更新 currentPage 和 pageSize
    },

    // 获取当前会话名称
    getCurrentSessionName() {
      if (!this.selectedSessionId) return ''
      const session = this.sessionList.find(s => s.sessionId === this.selectedSessionId)
      return session ? (session.sessionName || '未命名会话') : ''
    },

    // 获取发送者标签类型
    getSenderTagType(sender) {
      switch (sender) {
        case 'user':
          return 'primary'
        case 'assistant':
          return 'success'
        case 'system':
          return 'warning'
        default:
          return 'info'
      }
    },

    // 获取发送者图标
    getSenderIcon(sender) {
      switch (sender) {
        case 'user':
          return 'el-icon-user'
        case 'assistant':
          return 'el-icon-cpu'
        case 'system':
          return 'el-icon-setting'
        default:
          return 'el-icon-question'
      }
    },

    // 获取发送者显示名称
    getSenderDisplayName(sender) {
      switch (sender) {
        case 'user':
          return '用户'
        case 'assistant':
          return 'AI助手'
        case 'system':
          return '系统'
        default:
          return sender
      }
    },

    // 复制消息内容
    copyMessageContent() {
      if (!this.selectedMessage) return

      let content = ''
      if (this.selectedMessage.think) {
        content += `思考过程：\n${this.selectedMessage.think}\n\n`
      }
      content += `消息内容：\n${this.selectedMessage.content || '暂无内容'}`

      // 创建临时文本区域来复制内容
      const textArea = document.createElement('textarea')
      textArea.value = content
      document.body.appendChild(textArea)
      textArea.select()

      try {
        document.execCommand('copy')
        Message({
          message: '内容已复制到剪贴板',
          type: 'success'
        })
      } catch (err) {
        Message({
          message: '复制失败，请手动复制',
          type: 'error'
        })
      } finally {
        document.body.removeChild(textArea)
      }
    },

    // 获取行样式类名
    getRowClassName({ row }) {
      return `sender-${row.sender}`
    },

    // 格式化思考过程
    formatThink(think) {
      if (!think) return '暂无思考过程'
      // 移除多余的空白字符和换行符
      const cleanThink = think.replace(/\s+/g, ' ').trim()
      if (cleanThink.length <= 50) return cleanThink
      // 尝试在句号或感叹号处截断
      const cutIndex = cleanThink.substring(0, 47).lastIndexOf('。')
      if (cutIndex > 20) {
        return cleanThink.substring(0, cutIndex + 1)
      }
      return cleanThink.substring(0, 47) + '...'
    },

    // 格式化消息内容
    formatContent(content) {
      if (!content) return '暂无内容'
      // 移除多余的空白字符，但保留换行符的结构
      const cleanContent = content.replace(/[ \t]+/g, ' ').replace(/\n\s*\n/g, '\n').trim()
      if (cleanContent.length <= 120) return cleanContent

      // 尝试在句号、问号或感叹号处截断
      const cutIndex = cleanContent.substring(0, 117).lastIndexOf('。')
      const cutIndex2 = cleanContent.substring(0, 117).lastIndexOf('？')
      const cutIndex3 = cleanContent.substring(0, 117).lastIndexOf('！')
      const bestCutIndex = Math.max(cutIndex, cutIndex2, cutIndex3)

      if (bestCutIndex > 50) {
        return cleanContent.substring(0, bestCutIndex + 1)
      }
      return cleanContent.substring(0, 117) + '...'
    },

    // 格式化创建时间
    formatCreateTime(time) {
      if (!time) return ''
      const date = new Date(time)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-item {
  margin-right: 10px;
}

.stats-container {
  margin-bottom: 20px;
}

.stats-card {
  background: #f8f9fa;
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.stats-label {
  font-weight: bold;
  color: #606266;
}

.stats-value {
  color: #409eff;
  font-weight: bold;
  margin-left: 5px;
}

/* 思考过程单元格样式 */
.think-cell {
  padding: 8px 0;
}

.message-think {
  display: block;
  padding: 8px 12px;
  background: #fdf6ec;
  border-radius: 6px;
  color: #606266;
  font-size: 13px;
  line-height: 1.4;
  min-height: 20px;
  word-wrap: break-word;
  white-space: pre-wrap;
  transition: all 0.3s ease;
}

.message-think:not(.has-content) {
  background: #f5f7fa;
  color: #c0c4cc;
  font-style: italic;
}

.message-think:hover.has-content {
  background: #faecd8;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(230, 162, 60, 0.15);
}

/* 消息内容单元格样式 */
.content-cell {
  padding: 8px 0;
  position: relative;
}

.message-content {
  display: block;
  padding: 12px 16px;
  background: #f0f9ff;
  border-radius: 8px;
  color: #303133;
  font-size: 14px;
  line-height: 1.5;
  min-height: 24px;
  word-wrap: break-word;
  white-space: pre-wrap;
  transition: all 0.3s ease;
  position: relative;
}

.message-content.expandable {
  cursor: pointer;
}

.message-content:hover {
  background: #e1f3ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.content-actions {
  margin-top: 8px;
  text-align: right;
}

.content-actions .el-button {
  color: #409eff;
  font-size: 12px;
  padding: 2px 8px;
}

.content-actions .el-button:hover {
  background: #ecf5ff;
}

/* 消息详情对话框样式 */
.message-detail-dialog .el-dialog__body {
  padding: 20px;
  background: #fafbfc;
}

.message-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.info-card,
.content-card {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
}

.info-card {
  background: #ffffff;
}

.think-card {
  background: #fffbf0;
  border-color: #f0c78a;
}

.message-card {
  background: #f8fcff;
  border-color: #b3d8ff;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.card-header i {
  margin-right: 8px;
  color: #409eff;
}

.think-card .card-header i {
  color: #e6a23c;
}

.message-card .card-header i {
  color: #67c23a;
}

.detail-content {
  padding: 20px;
  border-radius: 8px;
  line-height: 1.6;
  word-wrap: break-word;
  background: #ffffff;
  border: 1px solid #e4e7ed;
}

.think-content {
  background: #fffbf0;
  border-color: #f0c78a;
}

.message-content-detail {
  background: #f8fcff;
  border-color: #b3d8ff;
}

.content-text {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: #2c3e50;
}

.time-display {
  color: #606266;
  font-size: 13px;
}

.time-display i {
  margin-right: 4px;
  color: #909399;
}

.dialog-footer {
  text-align: right;
  padding: 15px 0 0 0;
  border-top: 1px solid #e4e7ed;
}

.desc-label {
  font-weight: 600;
  color: #606266;
}

/* 表格整体样式优化 */
.el-table {
  margin-top: 20px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.el-table th {
  background: #f8f9fa;
  color: #606266;
  font-weight: 600;
  border-bottom: 2px solid #e4e7ed;
}

.el-table td {
  padding: 16px 12px;
  border-bottom: 1px solid #f0f0f0;
}

/* 表格行样式 */
.el-table .sender-user {
  background-color: #ecf5ff;
}

.el-table .sender-user:hover {
  background-color: #d9ecff !important;
}

.el-table .sender-assistant {
  background-color: #f0f9ff;
}

.el-table .sender-assistant:hover {
  background-color: #e1f3ff !important;
}

.el-table .sender-system {
  background-color: #fdf6ec;
}

.el-table .sender-system:hover {
  background-color: #faecd8 !important;
}

/* 表格行悬停效果 */
.el-table__row:hover {
  transform: translateY(-1px);
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.el-descriptions {
  margin-bottom: 20px;
}
</style>