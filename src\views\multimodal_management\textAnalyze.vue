<template>
  <div class="app-container">
    <div class="page-header">
      <h2>文本分析</h2>
      <!-- <p>上传文本文件进行AI智能分析</p> -->
    </div>

    <!-- 上传区域 -->
    <el-card class="upload-card">
      <div slot="header" class="card-header">
        <span>文本上传</span>
      </div>

      <el-upload
        class="upload-demo"
        drag
        :action="uploadAction"
        :headers="uploadHeaders"
        :before-upload="beforeUpload"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :file-list="fileList"
        accept=".txt,.doc,.docx,.pdf"
        :limit="1"
        :on-exceed="handleExceed"
      >
        <i class="el-icon-document"></i>
        <div class="el-upload__text">将文本文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">
          只能上传txt/doc/docx/pdf文件，且不超过10MB
        </div>
      </el-upload>
    </el-card>

    <!-- 分析结果区域 -->
    <el-card class="result-card" v-if="currentAnalysis">
      <div slot="header" class="card-header">
        <span>分析结果</span>
        <el-tag :type="getStatusTagType(currentAnalysis.status)" style="margin-left: 10px;">
          {{ getStatusText(currentAnalysis.status) }}
        </el-tag>
      </div>

      <div class="analysis-content">
        <div class="text-preview" v-if="currentAnalysis.content">
          <h4>原文本内容：</h4>
          <div class="content-preview">{{ formatContent(currentAnalysis.content) }}</div>
        </div>

        <div class="analysis-result" v-if="currentAnalysis.analysisResult">
          <h4>分析结果：</h4>
          <div class="result-content">{{ currentAnalysis.analysisResult }}</div>
        </div>

        <div class="analysis-loading" v-if="currentAnalysis.status === 1">
          <el-alert
            title="正在分析中，请稍候..."
            type="info"
            :closable="false"
            show-icon>
          </el-alert>
        </div>

        <div class="analysis-error" v-if="currentAnalysis.status === 3">
          <el-alert
            title="分析失败，请重试"
            type="error"
            :closable="false"
            show-icon>
          </el-alert>
        </div>
      </div>
    </el-card>

    <!-- 历史记录 -->
    <el-card class="history-card">
      <div slot="header" class="card-header">
        <span>历史记录</span>
        <el-button type="text" @click="refreshHistory">刷新</el-button>
      </div>

      <el-table
        v-loading="historyLoading"
        :data="historyList"
        style="width: 100%"
        @row-click="handleRowClick"
      >
        <el-table-column prop="filename" label="文件名" width="200" />
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="content" label="文本内容" width="220">
          <template #default="{ row }">
            <div class="content-preview" :title="row.content">{{ formatContent(row.content) }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="analysisResult" label="分析结果" min-width="320">
          <template #default="{ row }">
            <div class="result-preview" :title="row.analysisResult">{{ formatResult(row.analysisResult) }}</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button type="text" size="mini" @click.stop="handleAnalyze(row)">
              {{ row.status === 1 ? '分析中...' : '重新分析' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { uploadText } from '@/api/upload'
import { textAnalyze, getMultimodelByUserId } from '@/api/multimodal'
import { Message } from 'element-ui'
import { getToken } from '@/utils/auth'

export default {
  name: 'TextAnalyze',
  data() {
    return {
      fileList: [],
      currentAnalysis: null,
      historyList: [],
      historyLoading: false,
      uploadAction: process.env.VUE_APP_BASE_API + '/upload/text',
      uploadHeaders: {
        'token': getToken()
      }
    }
  },
  created() {
    this.fetchHistory()
  },
  methods: {
    // 上传前检查
    beforeUpload(file) {
      const allowedTypes = [
        'text/plain',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/pdf'
      ]
      const isAllowedType = allowedTypes.includes(file.type) ||
                           file.name.endsWith('.txt') ||
                           file.name.endsWith('.doc') ||
                           file.name.endsWith('.docx') ||
                           file.name.endsWith('.pdf')
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isAllowedType) {
        Message.error('只能上传txt/doc/docx/pdf文件!')
        return false
      }
      if (!isLt10M) {
        Message.error('上传文件大小不能超过 10MB!')
        return false
      }
      return true
    },

    // 上传成功
    async handleUploadSuccess(response, file) {
      if (response.code === 20000) {
        Message.success('上传成功!')
        this.fileList = []

        // 获取文件名并开始分析
        const filename = response.data
        await this.startAnalysis(filename)
      } else {
        Message.error(response.message || '上传失败')
      }
    },

    // 上传失败
    handleUploadError(err, file) {
      console.error('上传失败:', err)
      Message.error('上传失败，请重试')
    },

    // 文件数量超出限制
    handleExceed(files, fileList) {
      Message.warning('只能上传一个文件，请先删除已上传的文件')
    },

    // 开始分析
    async startAnalysis(filename) {
      try {
        const response = await textAnalyze(filename)
        if (response.code === 20000) {
          Message.success('分析已开始，请稍候...')
          // 刷新历史记录以获取最新状态
          await this.fetchHistory()
          // 设置当前分析项
          this.currentAnalysis = this.historyList.find(item => item.filename === filename)
          // 开始轮询检查分析状态
          this.pollAnalysisStatus(filename)
        } else {
          Message.error(response.message || '分析启动失败')
        }
      } catch (error) {
        console.error('分析失败:', error)
        Message.error('分析失败，请重试')
      }
    },

    // 轮询分析状态
    pollAnalysisStatus(filename) {
      const timer = setInterval(async () => {
        await this.fetchHistory()
        const analysis = this.historyList.find(item => item.filename === filename)
        if (analysis) {
          this.currentAnalysis = analysis
          // 如果分析完成或失败，停止轮询
          if (analysis.status === 2 || analysis.status === 3) {
            clearInterval(timer)
            if (analysis.status === 2) {
              Message.success('分析完成!')
            } else {
              Message.error('分析失败!')
            }
          }
        }
      }, 3000) // 每3秒检查一次

      // 设置最大轮询时间（5分钟）
      setTimeout(() => {
        clearInterval(timer)
      }, 300000)
    },

    // 获取历史记录
    async fetchHistory() {
      this.historyLoading = true
      try {
        const response = await getMultimodelByUserId()
        if (response.code === 20000) {
          // 只显示文本类型的记录 (fileType === 2)
          this.historyList = (response.data || []).filter(item => item.fileType === 2)
        } else {
          this.historyList = []
          Message.warning(response.message || '获取历史记录失败')
        }
      } catch (error) {
        console.error('获取历史记录失败:', error)
        Message.error('获取历史记录失败')
        this.historyList = []
      } finally {
        this.historyLoading = false
      }
    },

    // 刷新历史记录
    async refreshHistory() {
      await this.fetchHistory()
      Message.success('刷新成功')
    },

    // 点击行查看详情
    handleRowClick(row) {
      this.currentAnalysis = row
    },

    // 重新分析
    async handleAnalyze(row) {
      if (row.status === 1) {
        Message.warning('正在分析中，请稍候...')
        return
      }
      await this.startAnalysis(row.filename)
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      switch (status) {
        case 1:
          return 'warning'
        case 2:
          return 'success'
        case 3:
          return 'danger'
        default:
          return 'info'
      }
    },

    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case 1:
          return '分析中'
        case 2:
          return '分析完成'
        case 3:
          return '分析失败'
        default:
          return '未知状态'
      }
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    // 格式化文本内容
    formatContent(content) {
      if (!content) return '暂无内容'
      // 移除多余的空白字符和换行符
      const cleanContent = content.replace(/\s+/g, ' ').trim()
      if (cleanContent.length <= 80) return cleanContent
      return cleanContent.substring(0, 80) + '...'
    },

    // 格式化分析结果
    formatResult(result) {
      if (!result) return '暂无结果'
      // 移除多余的空白字符和换行符
      const cleanResult = result.replace(/\s+/g, ' ').trim()
      if (cleanResult.length <= 120) return cleanResult
      return cleanResult.substring(0, 120) + '...'
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
}

.upload-card,
.result-card,
.history-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.upload-demo {
  width: 100%;
}

.analysis-content {
  padding: 20px 0;
}

.text-preview {
  margin-bottom: 20px;
}

.text-preview h4 {
  margin-bottom: 10px;
  color: #303133;
}

.content-preview {
  padding: 15px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  line-height: 1.6;
  word-wrap: break-word;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
}

.analysis-result h4 {
  margin-bottom: 10px;
  color: #303133;
}

.result-content {
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
  line-height: 1.6;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.analysis-loading,
.analysis-error {
  margin-top: 20px;
}

.result-preview {
  color: #606266;
  line-height: 1.4;
}

/* 表格中的内容预览样式 */
.el-table .content-preview {
  display: block;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.5;
  padding: 8px 0;
}

.el-table .result-preview {
  display: block;
  max-width: 280px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.5;
  padding: 8px 0;
  color: #606266;
}

/* 表格行高度调整 */
.el-table .el-table__row {
  height: auto;
  min-height: 50px;
}

.el-table .el-table__cell {
  padding: 12px 0;
  vertical-align: middle;
}

/* 鼠标悬停时显示完整内容 */
.el-table .content-preview:hover,
.el-table .result-preview:hover {
  white-space: normal;
  word-wrap: break-word;
  max-width: none;
  background-color: #f5f7fa;
  padding: 8px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;
  position: relative;
}
</style>