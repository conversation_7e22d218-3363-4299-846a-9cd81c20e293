<template>
  <div class="navbar">
    <hamburger :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />

    <breadcrumb class="breadcrumb-container" />

    <div class="right-menu">
      <el-dropdown class="avatar-container" trigger="click">
        <div class="avatar-wrapper">
          <img
            v-if="avatar && avatar !== ''"
            :src="getAvatarUrl(avatar)"
            class="user-avatar"
            alt="avatar"
            @error="handleAvatarError"
          >
          <div v-else class="default-avatar">
            <i class="el-icon-user-solid"></i>
          </div>
          <span class="username">{{ name || 'Admin' }}</span>
          <i class="el-icon-caret-bottom"></i>
        </div>
        <el-dropdown-menu slot="dropdown" class="user-dropdown">
          <router-link to="/">
            <el-dropdown-item>首页</el-dropdown-item>
          </router-link>
          <router-link to="/about_author/author">
            <el-dropdown-item>关于作者</el-dropdown-item>
          </router-link>
          <el-dropdown-item divided @click.native="logout">
            <span style="display:block;">退出</span>
          </el-dropdown-item>
          <el-dropdown-item @click.native="deleteAccount">
            <span style="display:block;">注销</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'

export default {
  name: 'Navbar',
  components: {
    Breadcrumb,
    Hamburger
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'name'
    ])
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      await this.$store.dispatch('user/logout')
      this.$router.push(`/login?redirect=${this.$route.fullPath}`)
    },
    async deleteAccount() {
      this.$confirm('注销账户将永久删除您的所有数据，此操作不可恢复。确定要注销账户吗？', '警告', {
        confirmButtonText: '确定注销',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }).then(async () => {
        try {
          await this.$store.dispatch('user/deleteAccount')
          this.$message.success('账户注销成功')
          this.$router.push('/login')
        } catch (error) {
          this.$message.error('注销失败，请重试')
        }
      }).catch(() => {
        this.$message.info('已取消注销')
      })
    },
    getAvatarUrl(avatar) {
      // 如果avatar是相对路径，需要拼接基础URL
      if (avatar && avatar.startsWith('/')) {
        const fullUrl = `${process.env.VUE_APP_BASE_API}${avatar}`
        console.log('Avatar URL:', fullUrl)
        return fullUrl
      }
      console.log('Avatar URL (direct):', avatar)
      return avatar
    },
    handleAvatarError(e) {
      console.log('Avatar load error:', e)
      // 头像加载失败时，设置为默认头像
      e.target.src = 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif'
    }
  },
  mounted() {
    console.log('Navbar mounted - Avatar:', this.avatar, 'Name:', this.name)
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;
        display: flex;
        align-items: center;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          object-fit: cover;
        }

        .default-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background-color: #f0f0f0;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #5a5e66;
          font-size: 20px;
        }

        .username {
          margin-left: 8px;
          color: #5a5e66;
          font-size: 14px;
          font-weight: 500;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          margin-left: 8px;
          font-size: 12px;
          color: #5a5e66;
        }
      }
    }
  }
}
</style>

