/**
 * 简单的 Markdown 解析器
 * 支持常见的 Markdown 语法
 */

export class MarkdownParser {
  constructor() {
    // 代码块正则表达式
    this.codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g
    this.inlineCodeRegex = /`([^`]+)`/g
    
    // 标题正则表达式
    this.headerRegex = /^(#{1,6})\s+(.+)$/gm
    
    // 列表正则表达式
    this.unorderedListRegex = /^[\s]*[-*+]\s+(.+)$/gm
    this.orderedListRegex = /^[\s]*\d+\.\s+(.+)$/gm
    
    // 引用正则表达式
    this.blockquoteRegex = /^>\s+(.+)$/gm
    
    // 链接和图片正则表达式
    this.linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g
    this.imageRegex = /!\[([^\]]*)\]\(([^)]+)\)/g
    
    // 强调正则表达式
    this.boldRegex = /\*\*(.*?)\*\*/g
    this.italicRegex = /\*(.*?)\*/g
    this.strikethroughRegex = /~~(.*?)~~/g
    
    // 分割线正则表达式
    this.hrRegex = /^---+$/gm
    
    // 表格正则表达式
    this.tableRegex = /^\|(.+)\|\s*\n\|[-\s|:]+\|\s*\n((?:\|.+\|\s*\n?)*)/gm
  }

  /**
   * 解析 Markdown 文本为 HTML
   * @param {string} markdown - Markdown 文本
   * @returns {string} HTML 字符串
   */
  parse(markdown) {
    if (!markdown) return ''
    
    let html = markdown
    
    // 1. 处理代码块（必须最先处理，避免其他规则影响代码内容）
    html = this.parseCodeBlocks(html)
    
    // 2. 处理内联代码
    html = this.parseInlineCode(html)
    
    // 3. 处理表格
    html = this.parseTables(html)
    
    // 4. 处理标题
    html = this.parseHeaders(html)
    
    // 5. 处理列表
    html = this.parseLists(html)
    
    // 6. 处理引用
    html = this.parseBlockquotes(html)
    
    // 7. 处理图片（必须在链接之前处理）
    html = this.parseImages(html)
    
    // 8. 处理链接
    html = this.parseLinks(html)
    
    // 9. 处理强调
    html = this.parseEmphasis(html)
    
    // 10. 处理分割线
    html = this.parseHorizontalRules(html)
    
    // 11. 处理段落和换行
    html = this.parseParagraphs(html)
    
    return html
  }

  /**
   * 处理代码块
   */
  parseCodeBlocks(text) {
    return text.replace(this.codeBlockRegex, (match, language, code) => {
      const lang = language || 'text'
      const escapedCode = this.escapeHtml(code.trim())
      
      if (language) {
        return `<div class="code-block-with-header">
          <div class="code-header">${lang}</div>
          <pre><code>${escapedCode}</code></pre>
        </div>`
      } else {
        return `<pre><code>${escapedCode}</code></pre>`
      }
    })
  }

  /**
   * 处理内联代码
   */
  parseInlineCode(text) {
    return text.replace(this.inlineCodeRegex, (match, code) => {
      return `<code>${this.escapeHtml(code)}</code>`
    })
  }

  /**
   * 处理标题
   */
  parseHeaders(text) {
    return text.replace(this.headerRegex, (match, hashes, content) => {
      const level = hashes.length
      return `<h${level}>${content.trim()}</h${level}>`
    })
  }

  /**
   * 处理无序列表
   */
  parseUnorderedList(text) {
    const lines = text.split('\n')
    const result = []
    let inList = false
    
    for (let line of lines) {
      const match = line.match(/^[\s]*[-*+]\s+(.+)$/)
      if (match) {
        if (!inList) {
          result.push('<ul>')
          inList = true
        }
        result.push(`<li>${match[1]}</li>`)
      } else {
        if (inList) {
          result.push('</ul>')
          inList = false
        }
        result.push(line)
      }
    }
    
    if (inList) {
      result.push('</ul>')
    }
    
    return result.join('\n')
  }

  /**
   * 处理有序列表
   */
  parseOrderedList(text) {
    const lines = text.split('\n')
    const result = []
    let inList = false
    
    for (let line of lines) {
      const match = line.match(/^[\s]*\d+\.\s+(.+)$/)
      if (match) {
        if (!inList) {
          result.push('<ol>')
          inList = true
        }
        result.push(`<li>${match[1]}</li>`)
      } else {
        if (inList) {
          result.push('</ol>')
          inList = false
        }
        result.push(line)
      }
    }
    
    if (inList) {
      result.push('</ol>')
    }
    
    return result.join('\n')
  }

  /**
   * 处理列表
   */
  parseLists(text) {
    // 先处理无序列表，再处理有序列表
    text = this.parseUnorderedList(text)
    text = this.parseOrderedList(text)
    return text
  }

  /**
   * 处理引用
   */
  parseBlockquotes(text) {
    const lines = text.split('\n')
    const result = []
    let inBlockquote = false
    
    for (let line of lines) {
      const match = line.match(/^>\s+(.+)$/)
      if (match) {
        if (!inBlockquote) {
          result.push('<blockquote>')
          inBlockquote = true
        }
        result.push(`<p>${match[1]}</p>`)
      } else {
        if (inBlockquote) {
          result.push('</blockquote>')
          inBlockquote = false
        }
        result.push(line)
      }
    }
    
    if (inBlockquote) {
      result.push('</blockquote>')
    }
    
    return result.join('\n')
  }

  /**
   * 处理图片
   */
  parseImages(text) {
    return text.replace(this.imageRegex, (match, alt, src) => {
      return `<img src="${src}" alt="${alt}" />`
    })
  }

  /**
   * 处理链接
   */
  parseLinks(text) {
    return text.replace(this.linkRegex, (match, text, url) => {
      return `<a href="${url}" target="_blank" rel="noopener noreferrer">${text}</a>`
    })
  }

  /**
   * 处理强调
   */
  parseEmphasis(text) {
    // 处理粗体
    text = text.replace(this.boldRegex, '<strong>$1</strong>')
    // 处理斜体
    text = text.replace(this.italicRegex, '<em>$1</em>')
    // 处理删除线
    text = text.replace(this.strikethroughRegex, '<del>$1</del>')
    return text
  }

  /**
   * 处理分割线
   */
  parseHorizontalRules(text) {
    return text.replace(this.hrRegex, '<hr>')
  }

  /**
   * 处理表格
   */
  parseTables(text) {
    return text.replace(this.tableRegex, (match, header, separator, rows) => {
      const headerCells = header.split('|').map(cell => cell.trim()).filter(cell => cell)
      const rowsArray = rows.trim().split('\n').map(row => 
        row.split('|').map(cell => cell.trim()).filter(cell => cell)
      )
      
      let tableHtml = '<table>\n<thead>\n<tr>\n'
      headerCells.forEach(cell => {
        tableHtml += `<th>${cell}</th>\n`
      })
      tableHtml += '</tr>\n</thead>\n<tbody>\n'
      
      rowsArray.forEach(row => {
        if (row.length > 0) {
          tableHtml += '<tr>\n'
          row.forEach(cell => {
            tableHtml += `<td>${cell}</td>\n`
          })
          tableHtml += '</tr>\n'
        }
      })
      
      tableHtml += '</tbody>\n</table>'
      return tableHtml
    })
  }

  /**
   * 处理段落和换行
   */
  parseParagraphs(text) {
    // 分割成段落
    const paragraphs = text.split(/\n\s*\n/)
    
    return paragraphs.map(paragraph => {
      const trimmed = paragraph.trim()
      if (!trimmed) return ''
      
      // 如果已经是HTML标签，不需要包装
      if (trimmed.match(/^<(h[1-6]|ul|ol|blockquote|pre|table|hr|div)/)) {
        return trimmed
      }
      
      // 处理单个换行为<br>
      const withBreaks = trimmed.replace(/\n/g, '<br>')
      return `<p>${withBreaks}</p>`
    }).filter(p => p).join('\n\n')
  }

  /**
   * 转义HTML字符
   */
  escapeHtml(text) {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }
}

// 创建默认实例
export const markdownParser = new MarkdownParser()

/**
 * 快速解析 Markdown 的便捷函数
 * @param {string} markdown - Markdown 文本
 * @returns {string} HTML 字符串
 */
export function parseMarkdown(markdown) {
  return markdownParser.parse(markdown)
}
