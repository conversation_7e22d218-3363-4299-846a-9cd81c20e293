<template>
  <!-- 如果 isExternal 为真，即图标类名对应的路径是外部链接，显示带有外部图标样式的 div -->
  <div v-if="isExternal" :style="styleExternalIcon" class="svg-external-icon svg-icon" v-on="$listeners" />
  <!-- 如果 isExternal 为假，即图标类名对应的路径不是外部链接，显示 SVG 图标 -->
  <svg v-else :class="svgClass" aria-hidden="true" v-on="$listeners">
    <!-- 使用 SVG 的 <use> 标签引用定义好的图标，iconName 计算属性生成对应的图标 ID -->
    <use :xlink:href="iconName" />
  </svg>
</template>

<script>
// 文档说明：https://panjiachen.github.io/vue-element-admin-site/feature/component/svg-icon.html#usage
// 从 @/utils/validate 模块导入 isExternal 函数，用于判断路径是否为外部链接
import { isExternal } from '@/utils/validate'

export default {
  // 组件名称为 SvgIcon
  name: 'SvgIcon',
  // 定义组件接受的属性
  props: {
    // iconClass 属性，类型为字符串，是必填项，用于指定图标类名
    iconClass: {
      type: String,
      required: true
    },
    // className 属性，类型为字符串，默认值为空字符串，用于添加额外的类名
    className: {
      type: String,
      default: ''
    }
  },
  // 计算属性
  computed: {
    // 判断 iconClass 是否为外部链接的计算属性
    isExternal() {
      return isExternal(this.iconClass)
    },
    // 生成图标名称的计算属性，格式为 #icon-图标类名
    iconName() {
      return `#icon-${this.iconClass}`
    },
    // 生成 SVG 图标类名的计算属性，如果有额外的 className 属性，则添加到类名中
    svgClass() {
      if (this.className) {
        return 'svg-icon'+ this.className
      } else {
        return'svg-icon'
      }
    },
    // 生成外部图标样式的计算属性，用于设置背景遮罩
    styleExternalIcon() {
      return {
        mask: `url(${this.iconClass}) no-repeat 50% 50%`,
        '-webkit-mask': `url(${this.iconClass}) no-repeat 50% 50%`
      }
    }
  }
}
</script>

<style scoped>
/* // SVG 图标的基本样式，设置宽度、高度、垂直对齐方式、填充颜色和溢出处理 */
.svg-icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

/* // 外部图标样式，设置背景颜色和遮罩大小，并显示为内联块元素 */
.svg-external-icon {
  background-color: currentColor;
  mask-size: cover!important;
  display: inline-block;
}
</style>