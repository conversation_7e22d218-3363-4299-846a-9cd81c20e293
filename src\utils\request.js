// 引入 axios 库，用于发送 HTTP 请求
import axios from 'axios'
// 从 element-ui 中引入 MessageBox 和 Message 组件，用于显示弹窗和消息提示
import { MessageBox, Message } from 'element-ui'
// 引入 Vuex 存储实例，用于管理应用的状态
import store from '@/store'
// 引入获取 token 的工具函数
import { getToken } from '@/utils/auth'

// 创建一个 axios 实例，方便后续使用自定义的配置发送请求
const service = axios.create({
  // 设置请求的基础 URL，它会与具体的请求 URL 拼接在一起
  // process.env.VUE_APP_BASE_API 是从环境变量中获取的基础 API 地址
  baseURL: process.env.VUE_APP_BASE_API, 
  // 跨域请求时是否携带 cookie
  withCredentials: true, 
  // 设置请求超时时间
  timeout: 5000 
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    const token = getToken();
    if (token) {
      // 使用 Authorization Bearer 格式
      config.headers['Authorization'] = `Bearer ${token}`;
      // 同时保留 token 头，以防后端需要
      config.headers['token'] = token;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data

    // 根据你的后端返回格式调整
    if (res.code === 20000) {
      return res;
    }

    Message({
      message: res.message || 'Error',
      type: 'error',
      duration: 5 * 1000
    })

    return Promise.reject(new Error(res.message || 'Error'))
  },
  error => {
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

// 导出创建好的 axios 实例，供其他模块使用
export default service
