<template>
    <div :class="{'hidden':hidden}" class="pagination-container">
      <el-pagination
        :background="background"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="device === 'mobile'? layoutMobile : layout"
        :page-sizes="pageSizes"
        :total="total_1"
        v-bind="$attrs"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </template>
  
  <script>
  import { scrollTo } from '@/utils/scroll-to'
  import { mapGetters } from 'vuex'
  
  function getPageList() {
    let defaultPageList = [10, 50, 100, 200, 500]
    const newPage = Number(localStorage.getItem('pageSize') || 10)
    defaultPageList.push(newPage)
    defaultPageList = [...new Set(defaultPageList.sort((a, b) => a - b))]
    return defaultPageList
  }
  
  export default {
    name: 'Pagination',
    props: {
      total: {
        required: true,
        type: Number
      },
      page: {
        type: Number,
        default: 1
      },
      pagerCount: {
        type: Number,
        default: 10
      },
      limit: {
        type: Number,
        default: 10
      },
      pageSizes: {
        type: Array,
        default() {
          return getPageList()
        }
      },
      layout: {
        type: String,
        default: 'total, sizes, prev, pager, next, jumper'
      },
      layoutMobile: {
        type: String,
        default: 'prev, total, next, jumper'
      },
      background: {
        type: Boolean,
        default: true
      },
      autoScroll: {
        type: Boolean,
        default: true
      },
      hidden: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        tempCurrentPage: this.page,
        tempPageSize: this.limit
      }
    },
    computed: {
      total_1() {
        return this.total
      },
      currentPage: {
        get() {
          return this.tempCurrentPage
        },
        set(val) {
          if (this.tempCurrentPage !== val) {
            this.tempCurrentPage = val
            this.$emit('update:page', val)
            // console.log("pagenum change: ", val);
          }
        }
      },
      pageSize: {
        get() {
          return this.tempPageSize
        },
        set(val) {
          if (this.tempPageSize !== val) {
            this.tempPageSize = val
            this.$emit('update:limit', val)
            // console.log("pagesize change: ", val);
          }
        }
      },
      ...mapGetters(['device'])
    },
    methods: {
      resetPagination() {
        this.pageSize = 10
        this.currentPage = 1
      },
      handleSizeChange(val) {
        this.$emit('pagination', { page: this.currentPage, limit: val })
        if (this.autoScroll) {
          scrollTo(0, 800)
        }
      },
      handleCurrentChange(val) {
        this.$emit('pagination', { page: val, limit: this.pageSize })
        if (this.autoScroll) {
          scrollTo(0, 800)
        }
      }
    }
  }
  </script>
  
  <style scoped>
  .pagination-container {
    background: transparent;
    padding: 32px 16px;
  }
  .pagination-container.hidden {
    display: none;
  }
  </style>
  
  