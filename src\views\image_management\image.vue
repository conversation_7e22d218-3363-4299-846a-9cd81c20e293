<template>
  <div class="app-container">
    <div class="page-header">
      <h2>图片管理</h2>
      <!-- <p>管理和组织您的图片资源</p> -->
    </div>

    <!-- 搜索和操作区域 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.description"
        placeholder="搜索描述..."
        clearable
        class="filter-item"
        style="width: 200px"
      />
      <el-select
        v-model="listQuery.category"
        placeholder="选择分类"
        clearable
        class="filter-item"
        style="width: 150px; margin-left: 10px;"
      >
        <el-option label="baidu" value="baidu" />
        <el-option label="google" value="google" />
        <el-option label="sogou" value="sogou" />
        <el-option label="yahoo" value="yahoo" />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
        style="margin-left: 10px;"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
        style="margin-left: 10px;"
      >
        添加图片链接
      </el-button>
      <el-button
        class="filter-item"
        type="success"
        icon="el-icon-upload"
        @click="handleBatchUpload"
        style="margin-left: 10px;"
      >
        批量上传
      </el-button>
      <el-button
        class="filter-item"
        type="success"
        icon="el-icon-upload2"
        @click="handleImport"
        style="margin-left: 10px;"
      >
        导入Excel
      </el-button>
      <el-button
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        @click="handleBatchDelete"
        style="margin-left: 10px;"
        :disabled="selectedImages.length === 0"
      >
        批量删除
      </el-button>
      <el-button
        class="filter-item"
        type="warning"
        icon="el-icon-refresh"
        @click="fixImageUrls"
        style="margin-left: 10px;"
      >
        修复图片URL
      </el-button>
    </div>

    <!-- 视图切换 -->
    <div class="view-toggle">
      <el-radio-group v-model="viewMode" @change="handleViewModeChange">
        <el-radio-button label="grid">网格视图</el-radio-button>
        <el-radio-button label="table">表格视图</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 网格视图 -->
    <div v-if="viewMode === 'grid'" class="grid-view" v-loading="listLoading">
      <div class="image-grid">
        <div
          v-for="item in imageList"
          :key="item.imageId"
          class="image-card"
          :class="{ 'selected': isSelected(item) }"
          @click="toggleSelection(item)"
        >
          <div class="image-wrapper">
            <img
              :src="processImageUrl(item.url)"
              :alt="item.description"
              class="grid-image"
              @error="handleImageError"
              @click.stop="previewImage(item.url)"
            />
            <div class="image-overlay">
              <el-button type="text" size="mini" @click.stop="previewImage(item.url)">
                <i class="el-icon-zoom-in"></i>
              </el-button>
              <el-button type="text" size="mini" @click.stop="handleUpdate(item)">
                <i class="el-icon-edit"></i>
              </el-button>
              <el-button type="text" size="mini" @click.stop="handleDelete(item)">
                <i class="el-icon-delete"></i>
              </el-button>
            </div>
            <div class="selection-indicator" v-if="isSelected(item)">
              <i class="el-icon-check"></i>
            </div>
            <div class="image-status-indicator" :class="getImageStatusClass(item)">
              <i :class="getImageStatusIcon(item)"></i>
            </div>
          </div>
          <div class="image-info">
            <p class="image-description">{{ item.description || '无描述' }}</p>
            <div class="image-meta">
              <el-tag size="mini" :type="getCategoryTagType(item.category)">
                {{ item.category || '未分类' }}
              </el-tag>
              <span class="create-time">{{ formatTime(item.createTime) }}</span>
            </div>
          </div>
        </div>
      </div>

      <div v-if="imageList.length === 0 && !listLoading" class="empty-state">
        <i class="el-icon-picture-outline"></i>
        <p>暂无图片</p>
      </div>
    </div>

    <!-- 表格视图 -->
    <el-table
      v-if="viewMode === 'table'"
      style="margin-top: 20px;"
      v-loading="listLoading"
      element-loading-text="Loading"
      border
      :data="imageList"
      fit
      highlight-current-row
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" width="80" align="center" prop="imageId" />
      <el-table-column label="预览" width="120" align="center">
        <template #default="{ row }">
          <img
            :src="processImageUrl(row.url)"
            :alt="row.description"
            class="table-image"
            @click="previewImage(row.url)"
            @error="handleImageError"
          />
        </template>
      </el-table-column>
      <el-table-column label="图片地址" align="center" min-width="300">
        <template #default="{ row }">
          <el-link :href="row.url" target="_blank" type="primary" class="image-url">
            {{ row.url }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="描述" width="200" align="center" prop="description" />
      <el-table-column label="分类" width="120" align="center">
        <template #default="{ row }">
          <el-tag :type="getCategoryTagType(row.category)">{{ row.category || '未分类' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="180">
        <template #default="{ row }">
          <span>{{ formatTime(row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" width="180">
        <template #default="{ row }">
          <span>{{ formatTime(row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template #default="{ row }">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">编辑</el-button>
          <!-- <el-button type="success" size="mini" @click="previewImage(row.url)">预览</el-button> -->
          <el-button type="danger" size="mini" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="imageList.length > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="fetchData"
    />
    <!-- 添加/编辑图片对话框 -->
    <el-dialog
      :title="dialogStatus === 'create' ? '添加图片' : '编辑图片'"
      :visible.sync="dialogFormVisible"
      width="600px"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="100px"
        style="width: 500px; margin-left:50px;"
      >
        <el-form-item label="图片地址" prop="url">
          <el-input v-model="temp.url" placeholder="请输入完整的图片URL地址" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="temp.description" placeholder="请输入图片描述" />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-select v-model="temp.category" placeholder="请选择分类" style="width: 100%;">
            <el-option label="baidu" value="baidu" />
            <el-option label="google" value="google" />
            <el-option label="sogou" value="sogou" />
            <el-option label="yahoo" value="yahoo" />
          </el-select>
        </el-form-item>
        <el-form-item label="预览" v-if="temp.url">
          <img :src="processImageUrl(temp.url)" alt="预览" class="preview-image" @error="handleImageError" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 批量上传对话框 -->
    <el-dialog
      title="批量上传图片"
      :visible.sync="uploadDialogVisible"
      width="600px"
    >
      <el-form
        ref="uploadForm"
        :model="uploadForm"
        label-position="left"
        label-width="100px"
      >
        <el-form-item label="分类" prop="category">
          <el-select v-model="uploadForm.category" placeholder="请选择分类" style="width: 100%;">
            <el-option label="baidu" value="baidu" />
            <el-option label="google" value="google" />
            <el-option label="sogou" value="sogou" />
            <el-option label="yahoo" value="yahoo" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="uploadForm.description" placeholder="请输入图片描述" />
        </el-form-item>
      </el-form>

      <el-upload
        class="upload-demo"
        drag
        multiple
        :action="uploadAction"
        :headers="uploadHeaders"
        :before-upload="beforeImageUpload"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :on-change="handleFileChange"
        :file-list="uploadFileList"
        accept="image/*"
        :limit="10"
        :on-exceed="handleUploadExceed"
        :auto-upload="false"
        ref="imageUpload"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将图片文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">
          只能上传jpg/png/gif/bmp文件，单个文件不超过5MB，最多10个文件
        </div>
      </el-upload>

      <div slot="footer" class="dialog-footer">
        <el-button @click="uploadDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitBatchUpload">确定上传</el-button>
      </div>
    </el-dialog>

    <!-- Excel导入对话框 -->
    <el-dialog
      title="导入Excel文件"
      :visible.sync="importDialogVisible"
      width="500px"
    >
      <el-upload
        class="upload-demo"
        drag
        :action="importAction"
        :headers="uploadHeaders"
        :before-upload="beforeExcelUpload"
        :on-success="handleImportSuccess"
        :on-error="handleImportError"
        :file-list="importFileList"
        accept=".xlsx,.xls"
        :limit="1"
        :on-exceed="handleImportExceed"
        :auto-upload="false"
        ref="excelUpload"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将Excel文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">
          只能上传xlsx/xls文件，且不超过10MB
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="importDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitImport">确定导入</el-button>
      </div>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog
      title="图片预览"
      :visible.sync="previewVisible"
      width="80%"
      center
    >
      <div class="preview-container">
        <img :src="processImageUrl(previewImageUrl)" alt="预览图片" class="large-preview-image" @error="handleImageError" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getImageList, insertImage, deleteImage, updateImage, batchDeleteImages, importImagesByExcel, batchUploadImages } from '@/api/image'
import { Message, MessageBox } from 'element-ui'
import Pagination from '@/components/Pagination.vue'
import { getToken } from '@/utils/auth'

export default {
  name: 'ImageManagement',
  components: {
    Pagination
  },
  data() {
    return {
      imageList: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
        description: '',
        category: ''
      },
      selectedImages: [],
      viewMode: 'grid', // 'grid' 或 'table'
      dialogFormVisible: false,
      dialogStatus: '',
      temp: {
        imageId: undefined,
        url: '',
        description: '',
        category: ''
      },
      rules: {
        url: [
          { required: true, message: '请输入图片地址', trigger: 'blur' },
          { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入图片描述', trigger: 'blur' },
          { min: 1, max: 200, message: '描述长度在1到200个字符', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择分类', trigger: 'change' }
        ]
      },
      uploadDialogVisible: false,
      uploadForm: {
        category: '',
        description: ''
      },
      uploadFileList: [],
      uploadAction: process.env.VUE_APP_BASE_API + '/image/batchUploadImages',
      importDialogVisible: false,
      importFileList: [],
      importAction: process.env.VUE_APP_BASE_API + '/image/importImagesByExcel',
      uploadHeaders: {
        'token': getToken()
      },
      previewVisible: false,
      previewImageUrl: '',
      imageStatusMap: new Map() // 存储图片状态
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    // 获取图片列表
    async fetchData() {
      this.listLoading = true
      try {
        const response = await getImageList(
          this.listQuery.category,
          this.listQuery.description,
          this.listQuery.page,
          this.listQuery.limit
        )
        if (response.code === 20000) {
          // 适配后端响应格式：数据在 data.list 中，总数在 data.total 中
          this.imageList = response.data?.list || []
          this.total = response.data?.total || 0

          // 检查图片可访问性
          this.checkImageAccessibility()
        } else {
          this.imageList = []
          this.total = 0
          Message({
            message: response.message || '获取图片列表失败',
            type: 'warning'
          })
        }
      } catch (error) {
        console.error('获取图片列表失败:', error)
        Message({
          message: '获取图片列表失败',
          type: 'error'
        })
        this.imageList = []
        this.total = 0
      } finally {
        this.listLoading = false
      }
    },

    // 搜索过滤
    handleFilter() {
      this.listQuery.page = 1
      this.fetchData()
    },

    // 视图模式切换
    handleViewModeChange() {
      // 清空选择
      this.selectedImages = []
    },

    // 重置临时数据
    resetTemp() {
      this.temp = {
        imageId: undefined,
        url: '',
        description: '',
        category: ''
      }
    },

    // 新建图片
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },

    // 创建图片
    createData() {
      this.$refs['dataForm'].validate(async (valid) => {
        if (valid) {
          try {
            const response = await insertImage(this.temp)
            if (response.code === 20000) {
              Message({
                message: '图片创建成功',
                type: 'success'
              })
              this.dialogFormVisible = false
              this.fetchData()
            } else {
              Message({
                message: response.message || '创建图片失败',
                type: 'error'
              })
            }
          } catch (error) {
            console.error('创建图片失败:', error)
            Message({
              message: '创建图片失败',
              type: 'error'
            })
          }
        }
      })
    },

    // 编辑图片
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },

    // 更新图片数据
    updateData() {
      this.$refs['dataForm'].validate(async (valid) => {
        if (valid) {
          try {
            const response = await updateImage(this.temp)
            if (response.code === 20000) {
              Message({
                message: '图片更新成功',
                type: 'success'
              })
              this.dialogFormVisible = false
              this.fetchData()
            } else {
              Message({
                message: response.message || '更新图片失败',
                type: 'error'
              })
            }
          } catch (error) {
            console.error('更新图片失败:', error)
            Message({
              message: '更新图片失败',
              type: 'error'
            })
          }
        }
      })
    },

    // 删除单个图片
    handleDelete(row) {
      MessageBox.confirm(
        `确定要删除图片 "${row.description}" 吗？`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          const response = await deleteImage(row.imageId)
          if (response.code === 20000) {
            Message({
              message: '删除成功',
              type: 'success'
            })
            this.fetchData()
          } else {
            Message({
              message: response.message || '删除失败',
              type: 'error'
            })
          }
        } catch (error) {
          console.error('删除图片失败:', error)
          Message({
            message: '删除失败',
            type: 'error'
          })
        }
      }).catch(() => {
        Message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedImages.length === 0) {
        Message({
          message: '请选择要删除的图片',
          type: 'warning'
        })
        return
      }

      MessageBox.confirm(
        `确定要删除选中的 ${this.selectedImages.length} 张图片吗？`,
        '确认批量删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          const imageIds = this.selectedImages.map(image => image.imageId)
          const response = await batchDeleteImages(imageIds)
          if (response.code === 20000) {
            Message({
              message: response.data || '批量删除成功',
              type: 'success'
            })
            this.fetchData()
            this.selectedImages = []
          } else {
            Message({
              message: response.message || '批量删除失败',
              type: 'error'
            })
          }
        } catch (error) {
          console.error('批量删除失败:', error)
          Message({
            message: '批量删除失败',
            type: 'error'
          })
        }
      }).catch(() => {
        Message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },

    // 表格选择变化处理
    handleSelectionChange(val) {
      this.selectedImages = val
    },

    // 网格视图选择处理
    isSelected(item) {
      return this.selectedImages.some(selected => selected.imageId === item.imageId)
    },

    toggleSelection(item) {
      const index = this.selectedImages.findIndex(selected => selected.imageId === item.imageId)
      if (index > -1) {
        this.selectedImages.splice(index, 1)
      } else {
        this.selectedImages.push(item)
      }
    },

    // 批量上传
    handleBatchUpload() {
      this.uploadForm = {
        category: '',
        description: ''
      }
      this.uploadFileList = []
      this.uploadDialogVisible = true
    },

    // 图片上传前检查
    beforeImageUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isImage) {
        Message.error('只能上传图片文件!')
        return false
      }
      if (!isLt5M) {
        Message.error('上传图片大小不能超过 5MB!')
        return false
      }
      return true
    },

    // 提交批量上传
    async submitBatchUpload() {
      if (!this.uploadForm.category || !this.uploadForm.description) {
        Message.warning('请填写分类和描述')
        return
      }

      if (this.uploadFileList.length === 0) {
        Message.warning('请选择要上传的图片')
        return
      }

      try {
        const files = this.uploadFileList.map(item => item.raw)
        const response = await batchUploadImages(this.uploadForm.category, this.uploadForm.description, files)
        if (response.code === 20000) {
          Message.success(`批量上传成功，共上传 ${response.data.length} 张图片`)
          this.uploadDialogVisible = false
          this.uploadFileList = []
          this.fetchData()
        } else {
          Message.error(response.message || '批量上传失败')
        }
      } catch (error) {
        console.error('批量上传失败:', error)
        Message.error('批量上传失败，请重试')
      }
    },

    // 文件变化处理
    handleFileChange(file, fileList) {
      this.uploadFileList = fileList
    },

    // 上传成功（单个文件）
    handleUploadSuccess(response, file) {
      // 这个方法在使用自定义上传时不会被调用
    },

    // 上传失败
    handleUploadError(err, file) {
      console.error('上传失败:', err)
      Message.error('上传失败，请重试')
    },

    // 文件数量超出限制
    handleUploadExceed(files, fileList) {
      Message.warning('最多只能上传10个文件')
    },

    // 导入Excel
    handleImport() {
      this.importDialogVisible = true
      this.importFileList = []
    },

    // Excel上传前检查
    beforeExcelUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                     file.type === 'application/vnd.ms-excel' ||
                     file.name.endsWith('.xlsx') ||
                     file.name.endsWith('.xls')
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isExcel) {
        Message.error('只能上传Excel文件!')
        return false
      }
      if (!isLt10M) {
        Message.error('上传文件大小不能超过 10MB!')
        return false
      }
      return true
    },

    // 提交导入
    async submitImport() {
      if (this.importFileList.length === 0) {
        Message.warning('请选择要导入的Excel文件')
        return
      }

      try {
        const file = this.importFileList[0].raw
        const response = await importImagesByExcel(file)
        if (response.code === 20000) {
          Message.success(`导入成功，共导入 ${response.data.length} 条图片记录`)
          this.importDialogVisible = false
          this.importFileList = []
          this.fetchData()
        } else {
          Message.error(response.message || '导入失败')
        }
      } catch (error) {
        console.error('导入失败:', error)
        Message.error('导入失败，请重试')
      }
    },

    // 导入成功
    handleImportSuccess(response) {
      if (response.code === 20000) {
        Message.success(`导入成功，共导入 ${response.data.length} 条图片记录`)
        this.importDialogVisible = false
        this.importFileList = []
        this.fetchData()
      } else {
        Message.error(response.message || '导入失败')
      }
    },

    // 导入失败
    handleImportError(err) {
      console.error('导入失败:', err)
      Message.error('导入失败，请重试')
    },

    // 导入文件数量超出限制
    handleImportExceed() {
      Message.warning('只能上传一个文件，请先删除已上传的文件')
    },

    // 预览图片
    previewImage(imageUrl) {
      if (imageUrl) {
        this.previewImageUrl = imageUrl
        this.previewVisible = true
      }
    },

    // 图片加载错误处理
    handleImageError(event) {
      console.warn('图片加载失败:', event.target.src)

      // 记录失败的图片URL用于调试
      const failedUrl = event.target.src

      // 尝试不同的URL格式
      if (failedUrl && !failedUrl.includes('fallback-attempted')) {
        // 如果是相对路径，尝试添加基础URL
        if (!failedUrl.startsWith('http') && !failedUrl.startsWith('data:')) {
          const baseUrl = process.env.VUE_APP_BASE_API || 'http://localhost:8080'
          const newUrl = failedUrl.startsWith('/') ? `${baseUrl}${failedUrl}` : `${baseUrl}/${failedUrl}`
          event.target.src = `${newUrl}?fallback-attempted=true`
          return
        }

        // 如果是完整URL但加载失败，尝试通过代理访问
        if (failedUrl.startsWith('http') && !failedUrl.includes('localhost')) {
          // 将外部URL转换为通过本地代理访问
          event.target.src = `/api/proxy-image?url=${encodeURIComponent(failedUrl)}&fallback-attempted=true`
          return
        }
      }

      // 最终回退到默认图片
      event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwIiB5PSI1MCIgZm9udC1zaXplPSIxMiIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+WKoOi9veWksei0pTwvdGV4dD48L3N2Zz4='

      // 显示错误提示
      this.$message.warning(`图片加载失败: ${failedUrl}`)
    },

    // 获取分类标签类型
    getCategoryTagType(category) {
      switch (category) {
        case 'baidu':
          return 'primary'
        case 'google':
          return 'success'
        case 'sogou':
          return 'info'
        case 'yahoo':
          return 'warning'
        default:
          return 'info'
      }
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    // 处理图片URL
    processImageUrl(url) {
      if (!url) return ''

      // 如果已经是完整的URL，直接返回
      if (url.startsWith('http') || url.startsWith('data:')) {
        return url
      }

      // 如果是相对路径，添加基础URL
      const baseUrl = process.env.VUE_APP_BASE_API || 'http://localhost:8080'
      return url.startsWith('/') ? `${baseUrl}${url}` : `${baseUrl}/${url}`
    },

    // 检查图片URL是否可访问
    async checkImageUrl(url) {
      return new Promise((resolve) => {
        const img = new Image()
        img.onload = () => resolve(true)
        img.onerror = () => resolve(false)
        img.src = url

        // 设置超时
        setTimeout(() => resolve(false), 5000)
      })
    },

    // 获取可用的图片URL
    async getValidImageUrl(originalUrl) {
      if (!originalUrl) return ''

      // 首先尝试原始URL
      const processedUrl = this.processImageUrl(originalUrl)
      if (await this.checkImageUrl(processedUrl)) {
        return processedUrl
      }

      // 如果原始URL失败，尝试其他格式
      const alternatives = [
        originalUrl,
        originalUrl.startsWith('/') ? `http://localhost:8080${originalUrl}` : `http://localhost:8080/${originalUrl}`,
        originalUrl.startsWith('/') ? `${window.location.origin}${originalUrl}` : `${window.location.origin}/${originalUrl}`
      ]

      for (const altUrl of alternatives) {
        if (await this.checkImageUrl(altUrl)) {
          return altUrl
        }
      }

      // 如果都失败了，返回默认图片
      return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwIiB5PSI1MCIgZm9udC1zaXplPSIxMiIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+WKoOi9veWksei0pTwvdGV4dD48L3N2Zz4='
    },

    // 检查图片列表的可访问性
    async checkImageAccessibility() {
      if (!this.imageList || this.imageList.length === 0) return

      console.log('开始检查图片可访问性...')
      let accessibleCount = 0
      let inaccessibleCount = 0

      // 先设置所有图片为检查中状态
      for (const item of this.imageList) {
        this.imageStatusMap.set(item.imageId, 'checking')
      }

      // 强制更新视图
      this.$forceUpdate()

      for (const item of this.imageList) {
        if (item.url) {
          const isAccessible = await this.checkImageUrl(this.processImageUrl(item.url))
          if (isAccessible) {
            accessibleCount++
            this.imageStatusMap.set(item.imageId, 'accessible')
          } else {
            inaccessibleCount++
            this.imageStatusMap.set(item.imageId, 'inaccessible')
            console.warn(`图片无法访问: ${item.url}`)
          }
        } else {
          this.imageStatusMap.set(item.imageId, 'inaccessible')
          inaccessibleCount++
        }

        // 每检查完一张图片就更新视图
        this.$forceUpdate()
      }

      console.log(`图片可访问性检查完成: 可访问 ${accessibleCount} 张，无法访问 ${inaccessibleCount} 张`)

      if (inaccessibleCount > 0) {
        this.$message.warning(`发现 ${inaccessibleCount} 张图片无法访问，请检查图片URL或网络连接`)
      } else if (accessibleCount > 0) {
        this.$message.success(`所有 ${accessibleCount} 张图片都可以正常访问`)
      }
    },

    // 批量修复图片URL
    async fixImageUrls() {
      if (!this.imageList || this.imageList.length === 0) {
        this.$message.warning('没有图片需要修复')
        return
      }

      this.$message.info('开始修复图片URL...')
      let fixedCount = 0

      for (const item of this.imageList) {
        if (item.url) {
          const validUrl = await this.getValidImageUrl(item.url)
          if (validUrl !== this.processImageUrl(item.url)) {
            // 如果找到了有效的URL且与原URL不同，更新数据库
            try {
              const updateData = { ...item, url: validUrl }
              const response = await updateImage(updateData)
              if (response.code === 20000) {
                fixedCount++
              }
            } catch (error) {
              console.error('修复图片URL失败:', error)
            }
          }
        }
      }

      if (fixedCount > 0) {
        this.$message.success(`成功修复 ${fixedCount} 张图片的URL`)
        this.fetchData() // 重新获取数据
      } else {
        this.$message.info('没有需要修复的图片URL')
      }
    },

    // 获取图片状态样式类
    getImageStatusClass(item) {
      const status = this.imageStatusMap.get(item.imageId)
      switch (status) {
        case 'accessible':
          return 'status-success'
        case 'inaccessible':
          return 'status-error'
        case 'checking':
          return 'status-loading'
        default:
          return 'status-unknown'
      }
    },

    // 获取图片状态图标
    getImageStatusIcon(item) {
      const status = this.imageStatusMap.get(item.imageId)
      switch (status) {
        case 'accessible':
          return 'el-icon-success'
        case 'inaccessible':
          return 'el-icon-error'
        case 'checking':
          return 'el-icon-loading'
        default:
          return 'el-icon-question'
      }
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-item {
  margin-right: 10px;
}

.view-toggle {
  margin-bottom: 20px;
  text-align: right;
}

/* 网格视图样式 */
.grid-view {
  min-height: 400px;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.image-card {
  border: 2px solid transparent;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.image-card.selected {
  border-color: #409eff;
}

.image-wrapper {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.grid-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.grid-image:hover {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 5px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-card:hover .image-overlay {
  opacity: 1;
}

.selection-indicator {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 24px;
  height: 24px;
  background: #409eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
}

.image-status-indicator {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.status-success {
  background: #67c23a;
}

.status-error {
  background: #f56c6c;
}

.status-loading {
  background: #e6a23c;
  animation: pulse 1.5s infinite;
}

.status-unknown {
  background: #909399;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.image-info {
  padding: 15px;
}

.image-description {
  margin: 0 0 10px 0;
  font-size: 14px;
  line-height: 1.4;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.image-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.create-time {
  font-size: 12px;
  color: #909399;
}

/* 表格视图样式 */
.table-image {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.table-image:hover {
  transform: scale(1.1);
}

.image-url {
  max-width: 280px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-state i {
  font-size: 64px;
  margin-bottom: 20px;
}

.empty-state p {
  margin: 0;
  font-size: 16px;
}

/* 对话框样式 */
.dialog-footer {
  text-align: right;
}

.preview-image {
  max-width: 200px;
  max-height: 150px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

.upload-demo {
  width: 100%;
}

/* 预览容器样式 */
.preview-container {
  text-align: center;
}

.large-preview-image {
  max-width: 100%;
  max-height: 70vh;
  border-radius: 8px;
}

.el-table {
  margin-top: 20px;
}
</style>