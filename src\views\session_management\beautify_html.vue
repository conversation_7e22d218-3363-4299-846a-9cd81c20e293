<template>
  <div class="app-container">
    <div class="page-header">
      <h2>会话HTML美化</h2>
      <p class="page-description">批量美化当前用户的所有导出HTML文件，提升视觉效果和用户体验</p>
    </div>

    <!-- 美化功能卡片 -->
    <el-card class="beauty-card" shadow="hover">
      <div slot="header" class="card-header">
        <span class="card-title">
          <i class="el-icon-magic-stick"></i>
          HTML美化处理
        </span>
      </div>
      <div class="card-content">
        <div class="feature-description">
          <h3>美化功能包括：</h3>
          <ul class="feature-list">
            <li><i class="el-icon-check"></i> 优化HTML结构和格式</li>
            <li><i class="el-icon-check"></i> 美化CSS样式和布局</li>
            <li><i class="el-icon-check"></i> 增强视觉效果和交互体验</li>
            <li><i class="el-icon-check"></i> 统一样式风格和主题</li>
            <li><i class="el-icon-check"></i> 优化移动端适配</li>
          </ul>
        </div>

        <div class="operation-section">
          <div class="operation-info">
            <el-alert
              title="操作说明"
              type="info"
              :closable="false"
              show-icon
            >
              <p>点击下方按钮将对您账户下所有已导出的HTML文件进行批量美化处理。</p>
              <p>美化过程可能需要一些时间，请耐心等待。</p>
            </el-alert>
          </div>

          <div class="action-buttons">
            <el-button
              type="primary"
              size="large"
              icon="el-icon-magic-stick"
              :loading="beautifyLoading"
              @click="handleBeautifyHtml"
            >
              {{ beautifyLoading ? '正在美化中...' : '开始美化HTML' }}
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 美化历史记录 -->
    <el-card class="history-card" shadow="hover">
      <div slot="header" class="card-header">
        <span class="card-title">
          <i class="el-icon-time"></i>
          美化历史记录
        </span>
        <el-button
          type="text"
          icon="el-icon-refresh"
          @click="loadBeautifyHistory"
        >
          刷新
        </el-button>
      </div>
      <div class="card-content">
        <el-table
          :data="beautifyHistory"
          style="width: 100%"
          empty-text="暂无美化记录"
          v-loading="historyLoading"
        >
          <el-table-column prop="beautifyTime" label="美化时间" width="180">
            <template #default="{ row }">
              {{ formatTime(row.beautifyTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="processedCount" label="处理文件数" width="120" align="center" />
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="duration" label="耗时" width="100" align="center">
            <template #default="{ row }">
              {{ row.duration ? row.duration + 's' : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="message" label="结果信息" min-width="200">
            <template #default="{ row }">
              <el-tooltip :content="row.message" placement="top" :disabled="!row.message || row.message.length <= 50">
                <span class="message-text">{{ formatMessage(row.message) }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper" v-if="historyTotal > 0">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="historyQuery.page"
            :page-sizes="[10, 20, 50]"
            :page-size="historyQuery.limit"
            layout="total, sizes, prev, pager, next, jumper"
            :total="historyTotal"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { beautifyHtml } from '@/api/beautify_html'
import { Message } from 'element-ui'

export default {
  name: 'BeautifyHtml',
  data() {
    return {
      beautifyLoading: false,
      historyLoading: false,
      beautifyHistory: [],
      historyTotal: 0,
      historyQuery: {
        page: 1,
        limit: 10
      }
    }
  },
  created() {
    this.loadBeautifyHistory()
  },
  methods: {
    // 处理HTML美化
    async handleBeautifyHtml() {
      this.beautifyLoading = true
      const startTime = Date.now()

      try {
        const response = await beautifyHtml()
        const duration = Math.round((Date.now() - startTime) / 1000)

        if (response.code === 20000) {
          // 添加成功记录到历史
          this.addToHistory({
            beautifyTime: new Date(),
            processedCount: this.extractProcessedCount(response.data),
            status: 'success',
            duration: duration,
            message: response.data || '美化完成'
          })

          Message({
            message: 'HTML美化完成！',
            type: 'success',
            duration: 3000
          })
        } else {
          // 添加失败记录到历史
          this.addToHistory({
            beautifyTime: new Date(),
            processedCount: 0,
            status: 'failed',
            duration: duration,
            message: response.message || '美化失败'
          })

          Message({
            message: response.message || 'HTML美化失败',
            type: 'error',
            duration: 5000
          })
        }
      } catch (error) {
        const duration = Math.round((Date.now() - startTime) / 1000)
        console.error('HTML美化失败:', error)

        // 添加错误记录到历史
        this.addToHistory({
          beautifyTime: new Date(),
          processedCount: 0,
          status: 'error',
          duration: duration,
          message: '网络错误或服务异常'
        })

        Message({
          message: '美化过程中发生错误，请稍后重试',
          type: 'error',
          duration: 5000
        })
      } finally {
        this.beautifyLoading = false
      }
    },

    // 从响应消息中提取处理的文件数量
    extractProcessedCount(message) {
      if (!message) return 0

      // 尝试从消息中提取数字
      const match = message.match(/(\d+)/)
      return match ? parseInt(match[1]) : 1
    },

    // 添加到美化历史
    addToHistory(record) {
      this.beautifyHistory.unshift(record)
      // 只保留最近50条记录
      if (this.beautifyHistory.length > 50) {
        this.beautifyHistory = this.beautifyHistory.slice(0, 50)
      }
      this.historyTotal = this.beautifyHistory.length
      this.saveBeautifyHistory()
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    // 获取状态类型
    getStatusType(status) {
      switch (status) {
        case 'success':
          return 'success'
        case 'failed':
          return 'warning'
        case 'error':
          return 'danger'
        default:
          return 'info'
      }
    },

    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case 'success':
          return '成功'
        case 'failed':
          return '失败'
        case 'error':
          return '错误'
        default:
          return '未知'
      }
    },

    // 格式化消息
    formatMessage(message) {
      if (!message) return '-'
      if (message.length <= 50) return message
      return message.substring(0, 50) + '...'
    },

    // 保存美化历史到本地存储
    saveBeautifyHistory() {
      try {
        localStorage.setItem('beautifyHistory', JSON.stringify(this.beautifyHistory))
      } catch (error) {
        console.error('保存美化历史失败:', error)
      }
    },

    // 加载美化历史
    loadBeautifyHistory() {
      this.historyLoading = true
      try {
        const history = localStorage.getItem('beautifyHistory')
        if (history) {
          this.beautifyHistory = JSON.parse(history)
          this.historyTotal = this.beautifyHistory.length
        }
      } catch (error) {
        console.error('加载美化历史失败:', error)
        this.beautifyHistory = []
        this.historyTotal = 0
      } finally {
        this.historyLoading = false
      }
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.historyQuery.limit = val
      this.historyQuery.page = 1
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.historyQuery.page = val
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.beauty-card,
.history-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-title i {
  margin-right: 8px;
  font-size: 18px;
  color: #409EFF;
}

.card-content {
  padding: 20px 0;
}

.feature-description {
  margin-bottom: 30px;
}

.feature-description h3 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.feature-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.feature-list li {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.feature-list li i {
  margin-right: 8px;
  color: #67C23A;
  font-size: 16px;
}

.operation-section {
  border-top: 1px solid #EBEEF5;
  padding-top: 20px;
}

.operation-info {
  margin-bottom: 20px;
}

.operation-info .el-alert {
  border-radius: 6px;
}

.operation-info p {
  margin: 5px 0;
  font-size: 14px;
  line-height: 1.5;
}

.action-buttons {
  text-align: center;
}

.action-buttons .el-button {
  min-width: 160px;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
}

.message-text {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

/* 卡片悬停效果 */
.beauty-card:hover,
.history-card:hover {
  transform: translateY(-2px);
  transition: transform 0.3s ease;
}

/* 表格样式优化 */
.el-table {
  border-radius: 4px;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .feature-list li {
    font-size: 13px;
  }

  .action-buttons .el-button {
    width: 100%;
    min-width: auto;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

/* 加载状态样式 */
.el-button.is-loading {
  pointer-events: none;
}

/* 美化按钮特殊样式 */
.action-buttons .el-button--primary {
  background: linear-gradient(135deg, #409EFF 0%, #36A3F7 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
}

.action-buttons .el-button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
}

.action-buttons .el-button--primary:active {
  transform: translateY(0);
}
</style>