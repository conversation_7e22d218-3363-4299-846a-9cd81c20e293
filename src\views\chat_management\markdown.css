/* Markdown 样式 */
.markdown-content {
  line-height: 1.6;
  color: #333;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Can<PERSON>ell', '<PERSON><PERSON> Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

/* 标题样式 */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin: 16px 0 8px 0;
  font-weight: 600;
  line-height: 1.25;
  color: #1f2328;
}

.markdown-content h1 {
  font-size: 2em;
  border-bottom: 1px solid #d1d9e0;
  padding-bottom: 8px;
}

.markdown-content h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #d1d9e0;
  padding-bottom: 8px;
}

.markdown-content h3 {
  font-size: 1.25em;
}

.markdown-content h4 {
  font-size: 1em;
}

.markdown-content h5 {
  font-size: 0.875em;
}

.markdown-content h6 {
  font-size: 0.85em;
  color: #656d76;
}

/* 段落样式 */
.markdown-content p {
  margin: 8px 0;
}

/* 列表样式 */
.markdown-content ul,
.markdown-content ol {
  margin: 8px 0;
  padding-left: 24px;
}

.markdown-content li {
  margin: 4px 0;
}

.markdown-content ul li {
  list-style-type: disc;
}

.markdown-content ol li {
  list-style-type: decimal;
}

/* 嵌套列表 */
.markdown-content ul ul,
.markdown-content ol ol,
.markdown-content ul ol,
.markdown-content ol ul {
  margin: 0;
}

/* 代码样式 */
.markdown-content code {
  background-color: #f6f8fa;
  border-radius: 3px;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
  font-size: 85%;
  padding: 2px 4px;
  color: #d73a49;
}

.markdown-content pre {
  background-color: #f6f8fa;
  border-radius: 6px;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
  font-size: 85%;
  line-height: 1.45;
  margin: 16px 0;
  overflow: auto;
  padding: 16px;
}

.markdown-content pre code {
  background-color: transparent;
  border-radius: 0;
  color: #1f2328;
  font-size: 100%;
  padding: 0;
}

/* 代码块语言标识 */
.markdown-content .code-header {
  background-color: #f1f3f4;
  border-radius: 6px 6px 0 0;
  border-bottom: 1px solid #d1d9e0;
  color: #656d76;
  font-size: 12px;
  padding: 8px 16px;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
}

.markdown-content .code-block-with-header {
  margin: 16px 0;
  border-radius: 6px;
  border: 1px solid #d1d9e0;
  overflow: hidden;
}

.markdown-content .code-block-with-header pre {
  margin: 0;
  border-radius: 0;
  border: none;
}

/* 引用样式 */
.markdown-content blockquote {
  border-left: 4px solid #d1d9e0;
  color: #656d76;
  margin: 16px 0;
  padding: 0 16px;
}

.markdown-content blockquote p {
  margin: 8px 0;
}

/* 链接样式 */
.markdown-content a {
  color: #0969da;
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

/* 表格样式 */
.markdown-content table {
  border-collapse: collapse;
  border-spacing: 0;
  margin: 16px 0;
  width: 100%;
}

.markdown-content table th,
.markdown-content table td {
  border: 1px solid #d1d9e0;
  padding: 8px 12px;
  text-align: left;
}

.markdown-content table th {
  background-color: #f6f8fa;
  font-weight: 600;
}

.markdown-content table tr:nth-child(even) {
  background-color: #f6f8fa;
}

/* 分割线样式 */
.markdown-content hr {
  background-color: #d1d9e0;
  border: 0;
  height: 1px;
  margin: 24px 0;
}

/* 强调样式 */
.markdown-content strong,
.markdown-content b {
  font-weight: 600;
}

.markdown-content em,
.markdown-content i {
  font-style: italic;
}

/* 删除线样式 */
.markdown-content del,
.markdown-content s {
  text-decoration: line-through;
}

/* 图片样式 */
.markdown-content img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 8px 0;
}

/* 任务列表样式 */
.markdown-content .task-list-item {
  list-style-type: none;
  margin-left: -24px;
  padding-left: 24px;
}

.markdown-content .task-list-item input[type="checkbox"] {
  margin-right: 8px;
  vertical-align: middle;
}

/* 键盘按键样式 */
.markdown-content kbd {
  background-color: #f6f8fa;
  border: 1px solid #d1d9e0;
  border-radius: 3px;
  box-shadow: inset 0 -1px 0 #d1d9e0;
  color: #1f2328;
  display: inline-block;
  font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
  font-size: 11px;
  line-height: 10px;
  padding: 3px 5px;
  vertical-align: middle;
}

/* 高亮样式 */
.markdown-content mark {
  background-color: #fff8c5;
  padding: 2px 4px;
  border-radius: 2px;
}

/* 数学公式样式 */
.markdown-content .math {
  font-family: 'KaTeX_Main', 'Times New Roman', serif;
}

.markdown-content .math-inline {
  display: inline;
}

.markdown-content .math-block {
  display: block;
  text-align: center;
  margin: 16px 0;
}

/* 脚注样式 */
.markdown-content .footnote {
  font-size: 0.875em;
  color: #656d76;
}

.markdown-content .footnote-ref {
  color: #0969da;
  text-decoration: none;
  font-size: 0.875em;
  vertical-align: super;
}

.markdown-content .footnote-ref:hover {
  text-decoration: underline;
}