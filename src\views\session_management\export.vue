<template>
  <div class="app-container">
    <div class="page-header">
      <h2>导出管理</h2>
      <p class="page-description">管理和导出会话数据到不同格式</p>
    </div>

    <!-- 导出会话标题 -->
    <el-card class="export-card" shadow="hover">
      <div slot="header" class="card-header">
        <span class="card-title">
          <i class="el-icon-document"></i>
          导出会话标题
        </span>
      </div>
      <div class="card-content">
        <p class="card-description">导出当前用户所有会话的标题列表为TXT格式</p>
        <el-button
          type="primary"
          icon="el-icon-download"
          :loading="exportLoading.sessionNames"
          @click="handleExportSessionNames"
        >
          导出会话标题 (TXT)
        </el-button>
      </div>
    </el-card>

    <!-- 导出单个会话 -->
    <el-card class="export-card" shadow="hover">
      <div slot="header" class="card-header">
        <span class="card-title">
          <i class="el-icon-chat-dot-round"></i>
          导出会话内容
        </span>
      </div>
      <div class="card-content">
        <p class="card-description">选择会话并导出为不同格式</p>

        <!-- 会话选择 -->
        <div class="form-row">
          <label class="form-label">选择会话：</label>
          <el-select
            v-model="selectedSessionId"
            placeholder="请选择要导出的会话"
            style="width: 300px;"
            filterable
            clearable
          >
            <el-option
              v-for="session in sessions"
              :key="session.sessionId"
              :label="session.sessionName || session.sessionId"
              :value="session.sessionId"
            >
              <span style="float: left">{{ session.sessionName || '未命名会话' }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ session.sessionId }}</span>
            </el-option>
          </el-select>
        </div>

        <!-- 分类输入（用于XML、Excel、CSV格式） -->
        <div class="form-row">
          <label class="form-label">分类标签：</label>
          <el-input
            v-model="category"
            placeholder="请输入分类标签（用于XML、Excel、CSV格式）"
            style="width: 300px;"
            clearable
          />
        </div>

        <!-- 导出按钮组 -->
        <div class="export-buttons">
          <el-button
            type="success"
            icon="el-icon-download"
            :loading="exportLoading.html"
            :disabled="!selectedSessionId"
            @click="handleExportSession('html')"
          >
            导出为 HTML
          </el-button>

          <el-button
            type="warning"
            icon="el-icon-download"
            :loading="exportLoading.xml"
            :disabled="!selectedSessionId || !category"
            @click="handleExportSession('xml')"
          >
            导出为 XML
          </el-button>

          <el-button
            type="info"
            icon="el-icon-download"
            :loading="exportLoading.excel"
            :disabled="!selectedSessionId || !category"
            @click="handleExportSession('excel')"
          >
            导出为 Excel
          </el-button>

          <el-button
            type="primary"
            icon="el-icon-download"
            :loading="exportLoading.csv"
            :disabled="!selectedSessionId || !category"
            @click="handleExportSession('csv')"
          >
            导出为 CSV
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 导出历史记录 -->
    <el-card class="export-card" shadow="hover">
      <div slot="header" class="card-header">
        <span class="card-title">
          <i class="el-icon-time"></i>
          导出历史
        </span>
      </div>
      <div class="card-content">
        <el-table
          :data="exportHistory"
          style="width: 100%"
          empty-text="暂无导出记录"
        >
          <el-table-column prop="sessionId" label="会话ID" width="200" />
          <el-table-column prop="format" label="导出格式" width="100" />
          <el-table-column prop="category" label="分类" width="150" />
          <el-table-column prop="exportTime" label="导出时间" width="180">
            <template #default="{ row }">
              {{ formatTime(row.exportTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.status === 'success' ? 'success' : 'danger'">
                {{ row.status === 'success' ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script>
import {
  exportSessionNameTxt,
  exportSessionHtml,
  exportSessionXml,
  exportSessionExcel,
  exportSessionCsv
} from '@/api/export'
import { getChatSessionByUserId } from '@/api/session'
import { Message } from 'element-ui'

export default {
  name: 'ExportManagement',
  data() {
    return {
      sessions: [],
      selectedSessionId: '',
      category: '',
      exportLoading: {
        sessionNames: false,
        html: false,
        xml: false,
        excel: false,
        csv: false
      },
      exportHistory: []
    }
  },
  created() {
    this.fetchSessions()
    this.loadExportHistory()
  },
  methods: {
    // 获取会话列表
    async fetchSessions() {
      try {
        const response = await getChatSessionByUserId()
        if (response.code === 20000) {
          this.sessions = response.data || []
        } else {
          Message({
            message: response.message || '获取会话列表失败',
            type: 'warning'
          })
        }
      } catch (error) {
        console.error('获取会话列表失败:', error)
        Message({
          message: '获取会话列表失败',
          type: 'error'
        })
      }
    },

    // 导出会话标题
    async handleExportSessionNames() {
      this.exportLoading.sessionNames = true
      try {
        const response = await exportSessionNameTxt()
        if (response.code === 20000) {
          this.downloadFile(response.data, 'session_names.txt', 'text/plain')
          this.addToHistory('', 'TXT', '', 'success')
          Message({
            message: '会话标题导出成功',
            type: 'success'
          })
        } else {
          this.addToHistory('', 'TXT', '', 'failed')
          Message({
            message: response.message || '导出失败',
            type: 'error'
          })
        }
      } catch (error) {
        console.error('导出会话标题失败:', error)
        this.addToHistory('', 'TXT', '', 'failed')
        Message({
          message: '导出失败',
          type: 'error'
        })
      } finally {
        this.exportLoading.sessionNames = false
      }
    },

    // 导出会话内容
    async handleExportSession(format) {
      if (!this.selectedSessionId) {
        Message({
          message: '请选择要导出的会话',
          type: 'warning'
        })
        return
      }

      if ((format === 'xml' || format === 'excel' || format === 'csv') && !this.category) {
        Message({
          message: '请输入分类标签',
          type: 'warning'
        })
        return
      }

      this.exportLoading[format] = true
      try {
        let response
        let filename
        let mimeType

        switch (format) {
          case 'html':
            response = await exportSessionHtml(this.selectedSessionId)
            filename = `session_${this.selectedSessionId}.html`
            mimeType = 'text/html'
            break
          case 'xml':
            response = await exportSessionXml(this.selectedSessionId, this.category)
            filename = `session_${this.selectedSessionId}.xml`
            mimeType = 'application/xml'
            break
          case 'excel':
            response = await exportSessionExcel(this.selectedSessionId, this.category)
            filename = `session_${this.selectedSessionId}.xlsx`
            mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            break
          case 'csv':
            response = await exportSessionCsv(this.selectedSessionId, this.category)
            filename = `session_${this.selectedSessionId}.csv`
            mimeType = 'text/csv'
            break
        }

        if (response.code === 20000) {
          this.downloadFile(response.data, filename, mimeType)
          this.addToHistory(this.selectedSessionId, format.toUpperCase(), this.category, 'success')
          Message({
            message: `${format.toUpperCase()}格式导出成功`,
            type: 'success'
          })
        } else {
          this.addToHistory(this.selectedSessionId, format.toUpperCase(), this.category, 'failed')
          Message({
            message: response.message || '导出失败',
            type: 'error'
          })
        }
      } catch (error) {
        console.error(`导出${format}格式失败:`, error)
        this.addToHistory(this.selectedSessionId, format.toUpperCase(), this.category, 'failed')
        Message({
          message: '导出失败',
          type: 'error'
        })
      } finally {
        this.exportLoading[format] = false
      }
    },

    // 下载文件
    downloadFile(content, filename, mimeType) {
      const blob = new Blob([content], { type: mimeType })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    },

    // 添加到导出历史
    addToHistory(sessionId, format, category, status) {
      const historyItem = {
        sessionId: sessionId || '全部会话',
        format: format,
        category: category || '-',
        exportTime: new Date(),
        status: status
      }
      this.exportHistory.unshift(historyItem)
      // 只保留最近20条记录
      if (this.exportHistory.length > 20) {
        this.exportHistory = this.exportHistory.slice(0, 20)
      }
      this.saveExportHistory()
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    // 保存导出历史到本地存储
    saveExportHistory() {
      try {
        localStorage.setItem('exportHistory', JSON.stringify(this.exportHistory))
      } catch (error) {
        console.error('保存导出历史失败:', error)
      }
    },

    // 加载导出历史
    loadExportHistory() {
      try {
        const history = localStorage.getItem('exportHistory')
        if (history) {
          this.exportHistory = JSON.parse(history)
        }
      } catch (error) {
        console.error('加载导出历史失败:', error)
        this.exportHistory = []
      }
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.export-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-title i {
  margin-right: 8px;
  font-size: 18px;
  color: #409EFF;
}

.card-content {
  padding: 20px 0;
}

.card-description {
  margin: 0 0 20px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.form-label {
  display: inline-block;
  width: 100px;
  margin-right: 10px;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

.export-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 20px;
}

.export-buttons .el-button {
  min-width: 140px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .form-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .form-label {
    width: auto;
    margin-bottom: 5px;
  }

  .export-buttons {
    flex-direction: column;
  }

  .export-buttons .el-button {
    width: 100%;
  }
}

/* 卡片悬停效果 */
.export-card:hover {
  transform: translateY(-2px);
  transition: transform 0.3s ease;
}

/* 表格样式优化 */
.el-table {
  border-radius: 4px;
  overflow: hidden;
}

/* 按钮禁用状态样式 */
.el-button:disabled {
  opacity: 0.6;
}

/* 选择器样式 */
.el-select {
  width: 100%;
}

/* 输入框样式 */
.el-input {
  width: 100%;
}
</style>