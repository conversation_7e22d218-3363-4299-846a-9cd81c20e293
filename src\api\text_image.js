import request from '@/utils/request'

// 图像生成接口
export function imageGenerate(message, quality = 'standard') {
  return request({
    url: '/textImage/imageGenerate',
    method: 'post',
    params: {
      message: message,
      quality: quality
    }
  })
}

// 查询当前用户图像生成历史
export function getImageGenerateByUserId() {
  return request({
    url: '/textImage/getImageGenerateByUserId',
    method: 'get'
  })
}