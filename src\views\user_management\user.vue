<template>
    <div class="app-container">
    <div class="page-header">
      <h2>用户管理</h2>
    </div>
        <div class="filter-container">
            <el-input v-model="listQuery.userName" placeholder="用户名" clearable class="filter-item"
                style="width: 200px" />
            <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">查询</el-button>
            <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-edit"
                @click="handleCreate">添加用户</el-button>
            <el-button class="filter-item" type="danger" style="margin-left: 10px;" icon="el-icon-delete"
                @click="handleBatchDelete">批量删除</el-button>
        </div>

        <!-- 展示的表单 -->
        <el-table style="margin-top: 20px; width: 100%;" v-loading="listLoading" element-loading-text="Loading" border :data="users"
            fit highlight-current-row @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="用户ID" width="80" align="center" prop="userId" />
            <el-table-column label="用户名" min-width="150" align="center" prop="userName" />
            <el-table-column align="center" label="头像" width="100">
                <template #default="{ row }">
                    <img :src="getAvatarUrl(row.avatar)"
                         alt="头像"
                         style="width: 50px; height: 50px; border-radius: 50%; object-fit: cover;"
                         @error="handleImageError" />
                </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center" min-width="200">
                <template #default="{ row }">
                    <span>{{ formatCreateTime(row.createTime) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
                <template #default="{ row }">
                    <el-button type="primary" size="mini" @click="handleUpdate(row)">编辑</el-button>
                    <el-button type="danger" size="mini" @click="handleDelete(row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="count > 0" :total="count" :page.sync="listQuery.page" :limit.sync="listQuery.limit"
            @pagination="fetchData" />

        <!-- 添加/编辑的表单 -->
        <el-dialog :title="dialogStatus === 'create' ? '添加用户' : '编辑用户'" :visible.sync="dialogFormVisible">
            <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="70px"
                style="width: 400px; margin-left:50px;">
                <el-form-item label="用户名" prop="userName">
                    <el-input v-model="temp.userName" placeholder="请输入用户名" />
                </el-form-item>
                <el-form-item label="密码" prop="passWord" v-if="dialogStatus === 'create'">
                    <el-input v-model="temp.passWord" type="password" placeholder="请输入密码" />
                </el-form-item>
                <el-form-item label="头像">
                    <el-upload class="avatar-uploader" action="/upload/image" :headers="{ token }"
                        :show-file-list="false" :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload">
                        <img v-if="temp.avatar" :src="getAvatarUrl(temp.avatar)" class="avatar" @error="handleImageError" />
                        <i v-else class="el-icon-plus avatar-uploader-icon" />
                    </el-upload>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogFormVisible = false">取消</el-button>
                <el-button type="primary"
                    @click="dialogStatus === 'create' ? createData() : updateData()">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { deleteUser, getUserList, updateUser } from '@/api/user';
import { register as addUser } from '@/api/login';
import { getToken } from '@/utils/auth';
import Pagination from '@/components/Pagination';

export default {
    components: { Pagination },
    data() {
        return {
            token: '',
            listQuery: {
                page: 1,
                limit: 10,
                userName: ''
            },
            count: 0,
            users: [],
            listLoading: true,
            selectedRows: [],
            dialogFormVisible: false,
            dialogStatus: '',
            temp: {
                userId: '',
                userName: '',
                passWord: '',
                avatar: ''
            },
            rules: {
                userName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
                passWord: [{ required: true, message: '请输入密码', trigger: 'blur' }]
            }
        };
    },
    created() {
        this.fetchData();
    },
    mounted() {
        this.token = getToken();
    },
    methods: {
        fetchData() {
            this.listLoading = true;
            getUserList(this.listQuery).then(response => {
                this.users = response.data;
                this.count = response.data.length;
                this.listLoading = false;
            }).catch(() => {
                this.listLoading = false;
            });
        },
        handleFilter() {
            this.listQuery.page = 1;
            this.fetchData();
        },
        handleSelectionChange(selection) {
            this.selectedRows = selection;
        },
        handleUpdate(row) {
            this.temp = Object.assign({}, row);
            this.dialogStatus = 'update';
            this.dialogFormVisible = true;
            this.$nextTick(() => {
                this.$refs['dataForm'].clearValidate();
            });
        },
        handleCreate() {
            this.temp = {
                userId: '',
                userName: '',
                passWord: '',
                avatar: ''
            };
            this.dialogStatus = 'create';
            this.dialogFormVisible = true;
            this.$nextTick(() => {
                this.$refs['dataForm'].clearValidate();
            });
        },
        updateData() {
            this.$refs['dataForm'].validate(valid => {
                if (valid) {
                    updateUser(this.temp).then(() => {
                        this.fetchData();
                        this.dialogFormVisible = false;
                        this.$notify({ title: '成功', message: '更新成功', type: 'success', duration: 2000 });
                    });
                }
            });
        },
        createData() {
            this.$refs['dataForm'].validate(valid => {
                if (valid) {
                    addUser(this.temp).then(() => {
                        this.fetchData();
                        this.dialogFormVisible = false;
                        this.$notify({ title: '成功', message: '添加成功', type: 'success', duration: 2000 });
                    });
                }
            });
        },
        handleDelete(row) {
            this.$confirm('确定要删除所选项吗？', '警告', { type: 'warning' }).then(() => {
                deleteUser(row.userId).then(() => {
                    this.fetchData();
                    this.$notify({ title: '成功', message: '删除成功', type: 'success', duration: 2000 });
                });
            });
        },
        handleBatchDelete() {
            if (this.selectedRows.length === 0) {
                this.$message({ message: '请至少选择一项进行删除', type: 'warning' });
                return;
            }
            this.$confirm('确定要删除所选项吗？', '警告', { type: 'warning' }).then(() => {
                const idsToDelete = this.selectedRows.map(row => row.userId);
                Promise.all(idsToDelete.map(id => deleteUser(id))).then(() => {
                    this.fetchData();
                    this.$notify({ title: '成功', message: '删除成功', type: 'success', duration: 2000 });
                });
            });
        },
        handleAvatarSuccess(res) {
            if (res.code === 20000) {
                // 修改为直接使用 res.data，因为后端返回的就是图片路径
                this.temp.avatar = res.data;
            } else {
                this.$message.error('图片上传失败');
            }
        },
        beforeAvatarUpload(file) {
            const isImage = ['image/jpeg', 'image/png', 'image/bmp'].includes(file.type);
            const isLt2M = file.size / 1024 / 1024 < 2;
            if (!isImage) {
                this.$message.error('上传文件只能是 JPG、PNG 或 BMP 格式!');
            }
            if (!isLt2M) {
                this.$message.error('上传文件大小不能超过 2MB!');
            }
            return isImage && isLt2M;
        },
        getAvatarUrl(avatar) {
            if (!avatar) {
                return 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif';
            }
            // 如果是相对路径，添加服务器地址
            if (avatar.startsWith('/')) {
                return `${process.env.VUE_APP_BASE_API}${avatar}`;
            }
            return avatar;
        },
        handleImageError(event) {
            event.target.src = 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif';
        },
        formatCreateTime(time) {
            if (!time) return '';
            // 将 ISO 格式转换为标准格式，一行显示
            const date = new Date(time);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }
    }
};
</script>

<style scoped>
.app-container {
    padding: 20px;
}

.filter-container {
    margin-bottom: 20px;
}

.filter-item {
    margin-right: 10px;
}

.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.avatar-uploader .el-upload:hover {
    border-color: #409EFF;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}

.el-table {
    width: 100%;
}
</style>
