<template>
  <div class="login-container">
    <!-- 登录表单 -->
    <el-form
      v-if="!isRegister"
      ref="loginForm"
      :model="loginForm"
      :rules="loginRules"
      class="login-form"
      auto-complete="on"
      label-position="left"
    >
      <div class="title-container">
        <h3 class="title">LOGIN</h3>
      </div>

      <el-form-item prop="username">
        <el-input
          ref="username"
          v-model="loginForm.username"
          placeholder="用户名"
          name="username"
          type="text"
          tabindex="1"
          auto-complete="on"
        />
      </el-form-item>

      <el-form-item prop="password">
        <el-input
          :key="passwordType"
          ref="password"
          v-model="loginForm.password"
          :type="passwordType"
          placeholder="密码"
          name="password"
          tabindex="2"
          auto-complete="on"
          @keyup.enter.native="handleLogin"
        />
      </el-form-item>

      <el-button :loading="loading" type="primary" style="width:100%;margin-bottom:20px;" @click.native.prevent="handleLogin">登录</el-button>

      <div class="switch-form">
        <span>还没有账户？</span>
        <el-button type="text" @click="switchToRegister">立即注册</el-button>
      </div>
    </el-form>

    <!-- 注册表单 -->
    <el-form
      v-if="isRegister"
      ref="registerForm"
      :model="registerForm"
      :rules="registerRules"
      class="login-form"
      auto-complete="on"
      label-position="left"
    >
      <div class="title-container">
        <h3 class="title">REGISTER</h3>
      </div>

      <el-form-item prop="userName">
        <el-input
          ref="registerUsername"
          v-model="registerForm.userName"
          placeholder="用户名"
          name="userName"
          type="text"
          tabindex="1"
          auto-complete="on"
        />
      </el-form-item>

      <el-form-item prop="passWord">
        <el-input
          ref="registerPassword"
          v-model="registerForm.passWord"
          type="password"
          placeholder="密码"
          name="passWord"
          tabindex="2"
          auto-complete="on"
        />
      </el-form-item>

      <el-form-item prop="confirmPassword">
        <el-input
          ref="confirmPassword"
          v-model="registerForm.confirmPassword"
          type="password"
          placeholder="确认密码"
          name="confirmPassword"
          tabindex="3"
          auto-complete="on"
          @keyup.enter.native="handleRegister"
        />
      </el-form-item>

      <el-button :loading="loading" type="primary" style="width:100%;margin-bottom:20px;" @click.native.prevent="handleRegister">注册</el-button>

      <div class="switch-form">
        <span>已有账户？</span>
        <el-button type="text" @click="switchToLogin">立即登录</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { login, register } from '@/api/login'
import { setToken } from '@/utils/auth'

export default {
  name: 'Login',
  data() {
    // 确认密码验证规则
    const validateConfirmPassword = (_, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.registerForm.passWord) {
        callback(new Error('两次输入密码不一致'))
      } else {
        callback()
      }
    }

    return {
      isRegister: false, // 控制显示登录还是注册表单
      loginForm: {
        username: 'admin',
        password: 'admin'
      },
      registerForm: {
        userName: '',
        passWord: '',
        confirmPassword: ''
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', message: '请输入用户名' }],
        password: [{ required: true, trigger: 'blur', message: '请输入密码' }]
      },
      registerRules: {
        userName: [
          { required: true, trigger: 'blur', message: '请输入用户名' },
          { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        passWord: [
          { required: true, trigger: 'blur', message: '请输入密码' },
          // { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, trigger: 'blur', validator: validateConfirmPassword }
        ]
      },
      loading: false,
      passwordType: 'password'
    }
  },
  methods: {
    // 登录处理
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          const loginData = {
            userName: this.loginForm.username,
            passWord: this.loginForm.password
          }

          login(loginData).then(response => {
            this.loading = false
            if (response.code === 20000) {
              // 保存token到本地存储和Vuex
              setToken(response.data)
              this.$store.commit('user/SET_TOKEN', response.data)
              this.$message.success('登录成功')
              this.$router.push({ path: '/user_management/user' })
            } else {
              this.$message.error(response.message || '登录失败')
            }
          }).catch(error => {
            this.loading = false
            console.error('登录错误:', error)
          })
        }
      })
    },

    // 注册处理
    handleRegister() {
      this.$refs.registerForm.validate(valid => {
        if (valid) {
          this.loading = true
          const registerData = {
            userName: this.registerForm.userName,
            passWord: this.registerForm.passWord
          }

          register(registerData).then(response => {
            this.loading = false
            if (response.code === 20000) {
              this.$message.success('注册成功，请登录')
              this.switchToLogin()
              // 清空注册表单
              this.registerForm = {
                userName: '',
                passWord: '',
                confirmPassword: ''
              }
            } else {
              this.$message.error(response.message || '注册失败')
            }
          }).catch(error => {
            this.loading = false
            this.$message.error('注册失败，请重试')
            console.error('注册错误:', error)
          })
        }
      })
    },

    // 切换到注册表单
    switchToRegister() {
      this.isRegister = true
      this.loading = false
    },

    // 切换到登录表单
    switchToLogin() {
      this.isRegister = false
      this.loading = false
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  width: 100%;
  background-color: #000000;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;

  .login-form {
    position: relative;
    width: 520px;
    max-width: 100%;
    padding: 160px 35px 0;
    margin: 0 auto;
    overflow: hidden;
  }

  .title-container {
    position: relative;
    .title {
      font-size: 26px;
      color: #eee;
      margin: 0px auto 40px auto;
      text-align: center;
      font-weight: bold;
    }
  }

  .switch-form {
    text-align: center;
    color: #ccc;
    font-size: 14px;

    span {
      margin-right: 8px;
    }

    .el-button--text {
      color: #409EFF;
      font-size: 14px;
      padding: 0;

      &:hover {
        color: #66b1ff;
      }
    }
  }
}

// 输入框样式优化
:deep(.el-input__inner) {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #fff;

  &::placeholder {
    color: rgba(255, 255, 255, 0.6);
  }

  &:focus {
    border-color: #409EFF;
    background: rgba(255, 255, 255, 0.15);
  }
}

:deep(.el-form-item__error) {
  color: #f56c6c;
}
</style>