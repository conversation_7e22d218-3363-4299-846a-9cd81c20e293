
import { setToken } from '@/utils/auth'
const tokens = {
  admin: {
    token: 'admin-token'
  },
  editor: {
    token: 'editor-token'
  }
}

const users = {
  'admin-token': {
    roles: ['admin'],
    introduction: 'I am a super administrator',
    avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
    userName: 'Super Admin'
  },
  'editor-token': {
    roles: ['editor'],
    introduction: 'I am an editor',
    avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
    userName: 'Normal Editor'
  }
}

module.exports = [
  // user login
  {
    // url: '/vue-admin-template/user/login',
    url: '/user/login',
    type: 'post',
    response: config => {
      const { username } = config.body
      const token = tokens[username]

      // mock error
      if (!token) {
        return {
          code: 60204,
          message: 'Account and password are incorrect.'
        }
      }
      console.log(token,"tokentokentokentoken");
      
      // setToken(token)
      return {
        code: 20000,
        data: token
      }
    }
  },

  // get user info
  {
    // url: '/vue-admin-template/user/info\.*',
    url: '/user/getUserInfo',
    type: 'get',
    response: config => {
      // 从请求头中获取 token
      const authHeader = config.headers?.Authorization || config.headers?.authorization
      let token = config.query?.token

      if (authHeader && authHeader.startsWith('Bearer ')) {
        token = authHeader.substring(7)
      }

      const info = users[token]

      // mock error
      if (!info) {
        return {
          code: 50008,
          message: 'Login failed, unable to get user details.'
        }
      }

      return {
        code: 20000,
        data: info
      }
    }
  },

  // user logout
  {
    // url: '/vue-admin-template/user/logout',
    url: '/user/logout',
    type: 'post',
    response: _ => {
      return {
        code: 20000,
        data: 'success'
      }
    }
  }
]
