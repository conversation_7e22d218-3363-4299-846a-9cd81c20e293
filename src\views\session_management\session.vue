<template>
  <div class="app-container">
    <div class="page-header">
      <h2>会话管理</h2>
    </div>
    <div class="filter-container">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索会话..."
        clearable
        class="filter-item"
        style="width: 200px"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >
        新建会话
      </el-button>
      <el-button
        class="filter-item"
        type="danger"
        style="margin-left: 10px;"
        icon="el-icon-delete"
        @click="handleBatchDelete"
      >
        批量删除
      </el-button>
    </div>

    <!-- 会话列表表格 -->
    <el-table
      style="margin-top: 20px;"
      v-loading="listLoading"
      element-loading-text="Loading"
      border
      :data="paginatedSessions"
      fit
      highlight-current-row
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="会话ID" width="200" align="center" prop="sessionId" />
      <el-table-column label="会话名称" align="center" prop="sessionName">
        <template #default="{ row }">
          <span>{{ row.sessionName || '未命名会话' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最后消息" align="center" width="300">
        <template #default="{ row }">
          <el-tooltip :content="row.lastMessage" placement="top" :disabled="!row.lastMessage">
            <span class="last-message">{{ formatLastMessage(row.lastMessage) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="180">
        <template #default="{ row }">
          <span>{{ formatCreateTime(row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template #default="{ row }">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">编辑</el-button>
          <el-button type="danger" size="mini" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="handlePagination"
    />

    <!-- 新建/编辑会话对话框 -->
    <el-dialog
      :title="dialogStatus === 'create' ? '新建会话' : '编辑会话'"
      :visible.sync="dialogFormVisible"
      width="500px"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="100px"
        style="width: 400px; margin-left:50px;"
      >
        <el-form-item label="初始消息" prop="message">
          <el-input
            v-model="temp.message"
            type="textarea"
            :rows="4"
            placeholder="请输入初始消息（可选）"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { insertSession, deleteSession, getChatSessionByUserId } from '@/api/session'
import { Message, MessageBox } from 'element-ui'
import Pagination from '@/components/Pagination.vue'

export default {
  name: 'SessionManagement',
  components: {
    Pagination
  },
  data() {
    return {
      sessions: [],
      filteredSessions: [],
      searchKeyword: '',
      listLoading: true,
      dialogFormVisible: false,
      dialogStatus: '',
      temp: {
        sessionId: '',
        message: ''
      },
      selectedSessions: [],
      rules: {
        message: [
          { max: 500, message: '初始消息长度不能超过500个字符', trigger: 'blur' }
        ]
      },
      // 分页相关
      total: 0,
      listQuery: {
        page: 1,
        limit: 10
      }
    }
  },
  computed: {
    // 分页后的会话数据
    paginatedSessions() {
      const start = (this.listQuery.page - 1) * this.listQuery.limit
      const end = start + this.listQuery.limit
      return this.filteredSessions.slice(start, end)
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    // 获取会话列表
    async fetchData() {
      this.listLoading = true
      try {
        const response = await getChatSessionByUserId()
        if (response.code === 20000) {
          this.sessions = response.data || []
          this.filteredSessions = [...this.sessions]
          this.total = this.filteredSessions.length
        } else {
          this.sessions = []
          this.filteredSessions = []
          this.total = 0
          Message({
            message: response.message || '获取会话列表失败',
            type: 'warning'
          })
        }
      } catch (error) {
        console.error('获取会话列表失败:', error)
        Message({
          message: '获取会话列表失败',
          type: 'error'
        })
        this.sessions = []
        this.filteredSessions = []
        this.total = 0
      } finally {
        this.listLoading = false
      }
    },

    // 搜索过滤
    handleFilter() {
      if (!this.searchKeyword.trim()) {
        this.filteredSessions = [...this.sessions]
      } else {
        this.filteredSessions = this.sessions.filter(session =>
          (session.sessionId && session.sessionId.toLowerCase().includes(this.searchKeyword.toLowerCase())) ||
          (session.sessionName && session.sessionName.toLowerCase().includes(this.searchKeyword.toLowerCase())) ||
          (session.lastMessage && session.lastMessage.toLowerCase().includes(this.searchKeyword.toLowerCase()))
        )
      }
      // 更新总数并重置到第一页
      this.total = this.filteredSessions.length
      this.listQuery.page = 1
    },

    // 重置临时数据
    resetTemp() {
      this.temp = {
        sessionId: '',
        message: ''
      }
    },

    // 新建会话
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },

    // 创建会话
    createData() {
      this.$refs['dataForm'].validate(async (valid) => {
        if (valid) {
          try {
            const response = await insertSession(this.temp.message)
            if (response.code === 20000) {
              Message({
                message: '会话创建成功',
                type: 'success'
              })
              this.dialogFormVisible = false
              this.fetchData()
            } else {
              Message({
                message: response.message || '创建会话失败',
                type: 'error'
              })
            }
          } catch (error) {
            console.error('创建会话失败:', error)
            Message({
              message: '创建会话失败',
              type: 'error'
            })
          }
        }
      })
    },

    // 编辑会话（目前后端没有编辑接口，这里预留）
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },

    // 更新会话数据（预留功能）
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          Message({
            message: '编辑功能暂未实现',
            type: 'warning'
          })
          this.dialogFormVisible = false
        }
      })
    },

    // 删除单个会话
    handleDelete(row) {
      MessageBox.confirm(
        `确定要删除会话 "${row.sessionId}" 吗？`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          const response = await deleteSession(row.sessionId)
          if (response.code === 20000) {
            Message({
              message: '删除成功',
              type: 'success'
            })
            this.fetchData()
          } else {
            Message({
              message: response.message || '删除失败',
              type: 'error'
            })
          }
        } catch (error) {
          console.error('删除会话失败:', error)
          Message({
            message: '删除失败',
            type: 'error'
          })
        }
      }).catch(() => {
        Message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedSessions.length === 0) {
        Message({
          message: '请选择要删除的会话',
          type: 'warning'
        })
        return
      }

      MessageBox.confirm(
        `确定要删除选中的 ${this.selectedSessions.length} 个会话吗？`,
        '确认批量删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          const deletePromises = this.selectedSessions.map(session =>
            deleteSession(session.sessionId)
          )

          const results = await Promise.allSettled(deletePromises)

          let successCount = 0
          let failCount = 0

          results.forEach(result => {
            if (result.status === 'fulfilled' && result.value.code === 20000) {
              successCount++
            } else {
              failCount++
            }
          })

          if (successCount > 0) {
            Message({
              message: `成功删除 ${successCount} 个会话${failCount > 0 ? `，${failCount} 个删除失败` : ''}`,
              type: successCount === this.selectedSessions.length ? 'success' : 'warning'
            })
            this.fetchData()
          } else {
            Message({
              message: '批量删除失败',
              type: 'error'
            })
          }
        } catch (error) {
          console.error('批量删除失败:', error)
          Message({
            message: '批量删除失败',
            type: 'error'
          })
        }
      }).catch(() => {
        Message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },

    // 选择变化处理
    handleSelectionChange(val) {
      this.selectedSessions = val
    },

    // 格式化创建时间
    formatCreateTime(time) {
      if (!time) return ''
      const date = new Date(time)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    // 格式化最后消息（截取前50个字符）
    formatLastMessage(message) {
      if (!message) return '暂无消息'
      if (message.length <= 50) return message
      return message.substring(0, 50) + '...'
    },

    // 分页处理
    handlePagination() {
      // 分页组件会自动更新 listQuery.page 和 listQuery.limit
      // 这里可以添加额外的处理逻辑，比如当后端实现分页时调用 fetchData()
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-item {
  margin-right: 10px;
}

.dialog-footer {
  text-align: right;
}

.el-table {
  margin-top: 20px;
}

.last-message {
  display: inline-block;
  max-width: 280px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #606266;
  font-size: 13px;
  line-height: 1.4;
}
</style>