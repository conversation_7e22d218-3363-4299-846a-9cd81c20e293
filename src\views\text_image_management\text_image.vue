<template>
  <div class="app-container">
    <div class="page-header">
      <h2>文生图管理</h2>
      <!-- <p>通过文本描述生成AI图像</p> -->
    </div>

    <!-- 图像生成区域 -->
    <el-card class="generate-card">
      <div slot="header" class="card-header">
        <span>图像生成</span>
      </div>

      <el-form :model="generateForm" :rules="generateRules" ref="generateForm" label-width="100px">
        <el-form-item label="描述文本" prop="message">
          <el-input
            v-model="generateForm.message"
            type="textarea"
            :rows="4"
            placeholder="请输入您想要生成的图像描述，例如：一只可爱的小猫坐在花园里"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="图像质量" prop="quality">
          <el-select v-model="generateForm.quality" placeholder="请选择图像质量">
            <el-option label="标准质量" value="standard" />
            <el-option label="高清质量" value="hd" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            @click="handleGenerate"
            :loading="generating"
            :disabled="!generateForm.message.trim()"
          >
            {{ generating ? '生成中...' : '生成图像' }}
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 生成结果展示 -->
    <el-card class="result-card" v-if="currentGeneration">
      <div slot="header" class="card-header">
        <span>生成结果</span>
        <el-tag type="success" v-if="currentGeneration.imageUrl">生成成功</el-tag>
        <el-tag type="warning" v-else>生成中...</el-tag>
      </div>

      <div class="generation-result">
        <div class="prompt-info">
          <h4>提示词：</h4>
          <p class="prompt-text">{{ currentGeneration.message }}</p>
          <p class="quality-info">
            <span>质量：</span>
            <el-tag :type="getQualityTagType(currentGeneration.quality)">
              {{ getQualityText(currentGeneration.quality) }}
            </el-tag>
          </p>
        </div>

        <div class="image-result" v-if="currentGeneration.imageUrl">
          <h4>生成的图像：</h4>
          <div class="image-container">
            <img
              :src="currentGeneration.imageUrl"
              alt="生成的图像"
              class="generated-image"
              @click="previewImage(currentGeneration.imageUrl)"
            />
            <div class="image-actions">
              <el-button type="text" @click="downloadImage(currentGeneration.imageUrl, currentGeneration.message)">
                <i class="el-icon-download"></i> 下载
              </el-button>
              <el-button type="text" @click="previewImage(currentGeneration.imageUrl)">
                <i class="el-icon-zoom-in"></i> 预览
              </el-button>
            </div>
          </div>
        </div>

        <div class="generating-info" v-else>
          <el-alert
            title="正在生成图像，请稍候..."
            type="info"
            :closable="false"
            show-icon>
          </el-alert>
        </div>
      </div>
    </el-card>

    <!-- 历史记录 -->
    <el-card class="history-card">
      <div slot="header" class="card-header">
        <span>生成历史</span>
        <div>
          <el-button type="text" @click="refreshHistory">刷新</el-button>
          <el-button type="text" @click="clearHistory">清空历史</el-button>
        </div>
      </div>

      <div class="filter-container">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索提示词..."
          clearable
          class="filter-item"
          style="width: 300px;"
        />
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
          style="margin-left: 10px;"
        >
          搜索
        </el-button>
      </div>

      <div class="image-grid" v-loading="historyLoading">
        <div
          v-for="item in filteredHistory"
          :key="item.imageId"
          class="image-item"
          @click="handleItemClick(item)"
        >
          <div class="image-wrapper">
            <img
              v-if="item.imageUrl"
              :src="item.imageUrl"
              :alt="item.message"
              class="history-image"
            />
            <div v-else class="image-placeholder">
              <i class="el-icon-picture-outline"></i>
              <span>生成中...</span>
            </div>
          </div>

          <div class="image-info">
            <p class="image-prompt">{{ formatPrompt(item.message) }}</p>
            <div class="image-meta">
              <el-tag size="mini" :type="getQualityTagType(item.quality)">
                {{ getQualityText(item.quality) }}
              </el-tag>
              <span class="create-time">{{ formatTime(item.createTime) }}</span>
            </div>
          </div>

          <div class="image-actions-overlay">
            <el-button
              type="text"
              size="mini"
              @click.stop="previewImage(item.imageUrl)"
              v-if="item.imageUrl"
            >
              预览
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click.stop="downloadImage(item.imageUrl, item.message)"
              v-if="item.imageUrl"
            >
              下载
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click.stop="regenerateImage(item)"
            >
              重新生成
            </el-button>
          </div>
        </div>
      </div>

      <div v-if="filteredHistory.length === 0 && !historyLoading" class="empty-state">
        <i class="el-icon-picture-outline"></i>
        <p>暂无生成记录</p>
      </div>
    </el-card>

    <!-- 图像预览对话框 -->
    <el-dialog
      title="图像预览"
      :visible.sync="previewVisible"
      width="80%"
      center
    >
      <div class="preview-container">
        <img :src="previewImageUrl" alt="预览图像" class="preview-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { imageGenerate, getImageGenerateByUserId } from '@/api/text_image'
import { Message, MessageBox } from 'element-ui'

export default {
  name: 'TextImageManagement',
  data() {
    return {
      generateForm: {
        message: '',
        quality: 'standard'
      },
      generateRules: {
        message: [
          { required: true, message: '请输入图像描述', trigger: 'blur' },
          { min: 5, message: '描述至少需要5个字符', trigger: 'blur' },
          { max: 500, message: '描述不能超过500个字符', trigger: 'blur' }
        ],
        quality: [
          { required: true, message: '请选择图像质量', trigger: 'change' }
        ]
      },
      generating: false,
      currentGeneration: null,
      historyList: [],
      filteredHistory: [],
      historyLoading: false,
      searchKeyword: '',
      previewVisible: false,
      previewImageUrl: ''
    }
  },
  created() {
    this.fetchHistory()
  },
  methods: {
    // 生成图像
    handleGenerate() {
      this.$refs.generateForm.validate(async (valid) => {
        if (valid) {
          this.generating = true
          try {
            const response = await imageGenerate(this.generateForm.message, this.generateForm.quality)
            if (response.code === 20000) {
              Message.success('图像生成请求已提交，请稍候...')

              // 创建当前生成项
              this.currentGeneration = {
                message: this.generateForm.message,
                quality: this.generateForm.quality,
                imageUrl: null // 初始为空，等待生成完成
              }

              // 刷新历史记录
              await this.fetchHistory()

              // 开始轮询检查生成状态
              this.pollGenerationStatus()
            } else {
              Message.error(response.message || '图像生成失败')
            }
          } catch (error) {
            console.error('图像生成失败:', error)
            Message.error('图像生成失败，请重试')
          } finally {
            this.generating = false
          }
        }
      })
    },

    // 轮询生成状态
    pollGenerationStatus() {
      const timer = setInterval(async () => {
        await this.fetchHistory()

        // 查找最新的生成记录
        const latestItem = this.historyList[0]
        if (latestItem && latestItem.message === this.currentGeneration.message) {
          if (latestItem.imageUrl) {
            // 生成完成
            this.currentGeneration.imageUrl = latestItem.imageUrl
            clearInterval(timer)
            Message.success('图像生成完成!')
          }
        }
      }, 3000) // 每3秒检查一次

      // 设置最大轮询时间（5分钟）
      setTimeout(() => {
        clearInterval(timer)
        if (this.currentGeneration && !this.currentGeneration.imageUrl) {
          Message.warning('图像生成超时，请查看历史记录')
        }
      }, 300000)
    },

    // 重置表单
    resetForm() {
      this.$refs.generateForm.resetFields()
      this.currentGeneration = null
    },

    // 获取历史记录
    async fetchHistory() {
      this.historyLoading = true
      try {
        const response = await getImageGenerateByUserId()
        if (response.code === 20000) {
          this.historyList = response.data || []
          this.filteredHistory = [...this.historyList]
        } else {
          this.historyList = []
          this.filteredHistory = []
          Message.warning(response.message || '获取历史记录失败')
        }
      } catch (error) {
        console.error('获取历史记录失败:', error)
        Message.error('获取历史记录失败')
        this.historyList = []
        this.filteredHistory = []
      } finally {
        this.historyLoading = false
      }
    },

    // 刷新历史记录
    async refreshHistory() {
      await this.fetchHistory()
      Message.success('刷新成功')
    },

    // 清空历史记录
    clearHistory() {
      MessageBox.confirm(
        '确定要清空所有生成历史吗？此操作不可恢复！',
        '确认清空',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        // 这里应该调用后端删除接口，目前只是前端清空
        this.historyList = []
        this.filteredHistory = []
        this.currentGeneration = null
        Message.success('历史记录已清空')
      }).catch(() => {
        Message.info('已取消清空')
      })
    },

    // 搜索过滤
    handleFilter() {
      if (!this.searchKeyword.trim()) {
        this.filteredHistory = [...this.historyList]
      } else {
        this.filteredHistory = this.historyList.filter(item =>
          item.message && item.message.toLowerCase().includes(this.searchKeyword.toLowerCase())
        )
      }
    },

    // 点击历史项
    handleItemClick(item) {
      this.currentGeneration = item
    },

    // 预览图像
    previewImage(imageUrl) {
      if (imageUrl) {
        this.previewImageUrl = imageUrl
        this.previewVisible = true
      }
    },

    // 下载图像
    downloadImage(imageUrl, prompt) {
      if (!imageUrl) {
        Message.warning('图像还未生成完成')
        return
      }

      try {
        const link = document.createElement('a')
        link.href = imageUrl
        link.download = `generated_image_${Date.now()}.png`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        Message.success('图像下载已开始')
      } catch (error) {
        console.error('下载失败:', error)
        Message.error('下载失败，请重试')
      }
    },

    // 重新生成图像
    async regenerateImage(item) {
      this.generateForm.message = item.message
      this.generateForm.quality = item.quality
      await this.handleGenerate()
    },

    // 获取质量标签类型
    getQualityTagType(quality) {
      switch (quality) {
        case 'standard':
          return 'info'
        case 'hd':
          return 'success'
        default:
          return 'info'
      }
    },

    // 获取质量文本
    getQualityText(quality) {
      switch (quality) {
        case 'standard':
          return '标准质量'
        case 'hd':
          return '高清质量'
        default:
          return '未知质量'
      }
    },

    // 格式化提示词
    formatPrompt(prompt) {
      if (!prompt) return '无描述'
      if (prompt.length <= 50) return prompt
      return prompt.substring(0, 50) + '...'
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
}

.generate-card,
.result-card,
.history-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.generation-result {
  padding: 20px 0;
}

.prompt-info h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.prompt-text {
  margin: 0 0 15px 0;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
  line-height: 1.6;
}

.quality-info {
  margin: 0;
}

.image-result {
  margin-top: 20px;
}

.image-result h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.image-container {
  position: relative;
  display: inline-block;
}

.generated-image {
  max-width: 100%;
  max-height: 400px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.3s ease;
}

.generated-image:hover {
  transform: scale(1.02);
}

.image-actions {
  margin-top: 10px;
  text-align: center;
}

.filter-container {
  margin-bottom: 20px;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  min-height: 200px;
}

.image-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.image-item:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.image-wrapper {
  width: 100%;
  height: 200px;
  overflow: hidden;
  position: relative;
}

.history-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  color: #909399;
}

.image-placeholder i {
  font-size: 48px;
  margin-bottom: 10px;
}

.image-info {
  padding: 15px;
}

.image-prompt {
  margin: 0 0 10px 0;
  font-size: 14px;
  line-height: 1.4;
  color: #303133;
}

.image-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.create-time {
  font-size: 12px;
  color: #909399;
}

.image-actions-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 5px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-item:hover .image-actions-overlay {
  opacity: 1;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-state i {
  font-size: 64px;
  margin-bottom: 20px;
}

.empty-state p {
  margin: 0;
  font-size: 16px;
}

.preview-container {
  text-align: center;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  border-radius: 8px;
}
</style>