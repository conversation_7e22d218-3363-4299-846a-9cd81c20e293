<template>
  <div class="author-container">
    <div class="content-wrapper">
      <!-- 头部信息 -->
      <div class="header-section">
        <div class="author-info">
          <div class="avatar-section">
            <div class="avatar">
              <i class="el-icon-user-solid"></i>
            </div>
          </div>
          <div class="info-section">
            <h1 class="project-title">Ollama 项目说明文档</h1>
            <div class="author-details">
              <p><strong>作者：</strong>Lillian</p>
              <p><strong>项目版本：</strong>0.0.1-SNAPSHOT</p>
              <p><strong>技术栈：</strong>Spring Boot 3.3.3 + Spring AI 1.0.0 + Java 17</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 项目概述 -->
      <div class="section">
        <h2><i class="el-icon-document"></i> 项目概述</h2>
        <p class="description">
          这是一个基于Spring Boot和Spring AI框架开发的智能聊天应用，集成了Ollama本地大语言模型和OpenAI API，
          提供多模态AI分析、聊天对话、文件处理、图像生成等功能。项目采用现代化的分层架构设计，
          支持用户认证、会话管理、消息存储等完整功能。
        </p>
      </div>

      <!-- 核心功能 -->
      <div class="section">
        <h2><i class="el-icon-star-on"></i> 核心功能</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <i class="el-icon-user-solid"></i>
            </div>
            <h3>用户管理</h3>
            <ul>
              <li><strong>用户注册/登录：</strong>完整的用户管理功能</li>
              <li><strong>JWT认证：</strong>基于JWT的无状态身份验证</li>
              <li><strong>权限控制：</strong>用户数据隔离，确保数据安全</li>
              <li><strong>个人信息：</strong>支持用户个人信息管理和修改</li>
            </ul>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="el-icon-chat-dot-round"></i>
            </div>
            <h3>会话管理</h3>
            <ul>
              <li><strong>会话创建：</strong>自动创建和管理聊天会话</li>
              <li><strong>会话导出：</strong>支持会话导出为HTML格式</li>
              <li><strong>HTML美化：</strong>会话HTML美化功能，提升阅读体验</li>
              <li><strong>历史记录：</strong>完整的会话历史记录管理</li>
            </ul>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="el-icon-message"></i>
            </div>
            <h3>消息管理</h3>
            <ul>
              <li><strong>消息存储：</strong>自动保存所有聊天消息</li>
              <li><strong>消息查询：</strong>支持按条件查询历史消息</li>
              <li><strong>消息分析：</strong>提供消息统计和分析功能</li>
              <li><strong>消息导出：</strong>支持消息数据导出功能</li>
            </ul>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="el-icon-picture-outline"></i>
            </div>
            <h3>多模态分析</h3>
            <ul>
              <li><strong>图像分析：</strong>上传图片进行AI分析和识别</li>
              <li><strong>文本分析：</strong>上传文本文件进行AI内容分析</li>
              <li><strong>文件处理：</strong>支持多种文件格式的上传和解析</li>
              <li><strong>历史记录：</strong>保存所有多模态分析的历史记录</li>
            </ul>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="el-icon-picture-outline-round"></i>
            </div>
            <h3>文生图管理</h3>
            <ul>
              <li><strong>文生图：</strong>基于文本描述生成图像（使用DALL-E 3模型）</li>
              <li><strong>质量控制：</strong>支持不同质量级别的图像生成</li>
              <li><strong>历史管理：</strong>保存用户的图像生成历史</li>
              <li><strong>图像下载：</strong>支持生成图像的下载和管理</li>
            </ul>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="el-icon-link"></i>
            </div>
            <h3>链接管理</h3>
            <ul>
              <li><strong>关键词链接：</strong>管理关键词与URL的映射关系</li>
              <li><strong>智能匹配：</strong>支持关键词的智能匹配和替换</li>
              <li><strong>缓存优化：</strong>使用缓存提升链接查询性能</li>
              <li><strong>批量管理：</strong>支持批量添加和管理链接</li>
            </ul>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="el-icon-picture"></i>
            </div>
            <h3>图片管理</h3>
            <ul>
              <li><strong>图片上传：</strong>支持多种图片格式的上传</li>
              <li><strong>批量操作：</strong>支持批量上传、删除图片</li>
              <li><strong>Excel导入：</strong>支持通过Excel批量导入图片链接</li>
              <li><strong>文件验证：</strong>完善的文件类型、大小验证机制</li>
            </ul>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <i class="el-icon-chat-dot-round"></i>
            </div>
            <h3>聊天管理</h3>
            <ul>
              <li><strong>智能对话：</strong>支持与AI进行文本对话</li>
              <li><strong>流式对话：</strong>实时流式输出AI回复，提升用户体验</li>
              <li><strong>多模型支持：</strong>可配置使用不同的AI模型（gemma3、llama3.2、deepseek-r1等）</li>
              <li><strong>对话历史：</strong>完整的对话历史记录和管理</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 技术架构 -->
      <div class="section">
        <h2><i class="el-icon-setting"></i> 技术架构</h2>
        <div class="tech-stack">
          <div class="tech-category">
            <h4>技术栈</h4>
            <div class="tech-tags">
              <el-tag type="primary">Spring Boot 3.3.3</el-tag>
              <el-tag type="success">Spring AI 1.0.0</el-tag>
              <el-tag type="info">MySQL 8.0</el-tag>
              <el-tag type="warning">MyBatis-Plus 3.5.5</el-tag>
              <el-tag type="danger">JWT (jjwt 0.11.5)</el-tag>
              <el-tag>Apache Tika 2.9.1</el-tag>
              <el-tag>Apache POI 5.2.3</el-tag>
              <el-tag>Lombok</el-tag>
              <el-tag>Commons-IO</el-tag>
              <el-tag>CommonMark</el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 配置说明 -->
      <div class="section">
        <h2><i class="el-icon-s-tools"></i> 配置说明</h2>
        <div class="config-section">
          <h4>环境变量</h4>
          <p class="description">项目需要配置以下环境变量：</p>
          <div class="config-list">
            <div class="config-item">
              <code>OPENAI_API_KEY</code>
              <span>OpenAI API密钥</span>
            </div>
            <div class="config-item">
              <code>MYSQL_PASSWORD</code>
              <span>MySQL数据库密码</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 部署运行 -->
      <div class="section">
        <h2><i class="el-icon-upload"></i> 部署运行</h2>
        <div class="deployment-section">
          <div class="requirements">
            <h4>环境要求</h4>
            <ul class="requirement-list">
              <li>Java 17+</li>
              <li>MySQL 8.0+</li>
              <li>Ollama服务（本地运行在11434端口）</li>
              <li>Maven 3.6+</li>
            </ul>
          </div>

          <div class="steps">
            <h4>运行步骤</h4>
            <ol class="step-list">
              <li>克隆项目到本地</li>
              <li>配置数据库连接和环境变量</li>
              <li>启动Ollama服务</li>
              <li>运行Maven命令：<code>mvn spring-boot:run</code></li>
              <li>访问 <a href="http://localhost:8080" target="_blank">http://localhost:8080</a></li>
            </ol>
          </div>
        </div>
      </div>
      <!-- 项目特色 -->
      <div class="section">
        <h2><i class="el-icon-trophy"></i> 项目特色</h2>
        <div class="highlights-grid">
          <div class="highlight-item">
            <h4>现代化架构</h4>
            <p>采用Spring Boot 3.3.3最新版本，集成Spring AI官方AI框架，使用Java 17新特性</p>
          </div>
          <div class="highlight-item">
            <h4>完善的功能设计</h4>
            <p>支持多种AI模型切换，流式对话提升用户体验，多模态分析能力，完整的用户权限管理</p>
          </div>
          <div class="highlight-item">
            <h4>优秀的代码质量</h4>
            <p>分层架构清晰，统一异常处理，完善的参数验证，详细的日志记录</p>
          </div>
          <div class="highlight-item">
            <h4>安全性考虑</h4>
            <p>JWT身份认证，数据权限隔离，文件上传安全验证，敏感信息环境变量管理</p>
          </div>
        </div>
      </div>

      <!-- 联系方式 -->
      <div class="section contact-section">
        <h2><i class="el-icon-phone"></i> 联系方式</h2>
        <div class="contact-info">
          <div class="contact-item">
            <strong>作者：</strong>Lillian
          </div>
          <div class="contact-item">
            <strong>项目地址：</strong><a href="#" target="_blank">未开源</a>
          </div>
          <div class="contact-item">
            <strong>技术支持：</strong><a href="mailto:"><EMAIL></a>
          </div>
        </div>
        <div class="update-info">
          <p><em>本文档最后更新时间：2025年7月30日</em></p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Author',
  data() {
    return {
      // 可以在这里添加一些动态数据
    }
  },
  mounted() {
    // 页面加载完成后的操作
    this.animateOnScroll()
  },
  methods: {
    // 滚动动画效果
    animateOnScroll() {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-in')
          }
        })
      })

      const sections = document.querySelectorAll('.section')
      sections.forEach(section => {
        observer.observe(section)
      })
    }
  }
}
</script>

<style scoped>
.author-container {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
  margin: 0;
  overflow: hidden;
}

.content-wrapper {
  width: 100%;
  height: 100vh;
  margin: 0;
  background: white;
  border-radius: 0;
  box-shadow: none;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 自定义滚动条样式 */
.content-wrapper::-webkit-scrollbar {
  width: 8px;
}

.content-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.content-wrapper::-webkit-scrollbar-thumb {
  /* background: #667eea; */
  border-radius: 4px;
  transition: background 0.3s ease;
}

.content-wrapper::-webkit-scrollbar-thumb:hover {
  background: #5a6fd8;
}

.header-section {
  color: rgb(0, 0, 0);
  padding: 30px 40px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 30px;
}

.avatar-section {
  flex-shrink: 0;
}

.avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: white;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.info-section {
  flex: 1;
}

.project-title {
  margin: 0 0 20px 0;
  font-size: 32px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.author-details p {
  margin: 8px 0;
  font-size: 16px;
  opacity: 0.9;
}

.section {
  padding: 30px 40px;
  border-bottom: 1px solid #f0f0f0;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease;
}

.section.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.section:last-child {
  border-bottom: none;
}

.section h2 {
  margin: 0 0 24px 0;
  font-size: 24px;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 12px;
}

.section h2 i {
  color: #667eea;
  font-size: 28px;
}

.description {
  font-size: 16px;
  line-height: 1.8;
  color: #606266;
  margin: 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin-top: 24px;
}

.feature-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 24px;
  /* border-left: 4px solid #667eea; */
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  margin-bottom: 16px;
}

.feature-icon i {
  font-size: 32px;
  color: #667eea;
}

.feature-card h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  color: #303133;
  font-weight: 600;
}

.feature-card ul {
  margin: 0;
  padding-left: 0;
  list-style: none;
}

.feature-card li {
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.6;
  color: #606266;
  position: relative;
  padding-left: 16px;
}

.feature-card li:before {
  content: "•";
  color: #667eea;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.tech-stack {
  margin-top: 24px;
}

.tech-category h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
  font-weight: 600;
}

.tech-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tech-tags .el-tag {
  margin: 0;
}

.config-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
  font-weight: 600;
}

.config-list {
  margin-top: 16px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.config-item code {
  background: #e9ecef;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  color: #495057;
  font-weight: 600;
}

.config-item span {
  color: #606266;
}

.deployment-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 24px;
}

.requirements h4,
.steps h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
  font-weight: 600;
}

.requirement-list,
.step-list {
  margin: 0;
  padding-left: 20px;
}

.requirement-list li,
.step-list li {
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.6;
  color: #606266;
}

.step-list li code {
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  color: #495057;
}

.step-list li a {
  color: #667eea;
  text-decoration: none;
}

.step-list li a:hover {
  text-decoration: underline;
}

.highlights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 24px;
}

.highlight-item {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 20px;
  border-radius: 8px;
  /* border-top: 3px solid #667eea; */
  transition: transform 0.3s ease;
}

.highlight-item:hover {
  transform: translateY(-2px);
}

.highlight-item h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #303133;
  font-weight: 600;
}

.highlight-item p {
  margin: 0;
  font-size: 14px;
  line-height: 1.6;
  color: #606266;
}

.contact-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.contact-info {
  margin-top: 24px;
}

.contact-item {
  margin-bottom: 12px;
  font-size: 16px;
  color: #303133;
}

.contact-item a {
  color: #667eea;
  text-decoration: none;
}

.contact-item a:hover {
  text-decoration: underline;
}

.update-info {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
  text-align: center;
}

.update-info p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .author-container {
    padding: 0;
  }

  .content-wrapper {
    width: 100%;
    height: 100vh;
  }

  .header-section {
    padding: 20px 15px;
  }

  .author-info {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .project-title {
    font-size: 24px;
  }

  .section {
    padding: 20px 15px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .feature-card {
    padding: 16px;
  }

  .deployment-section {
    grid-template-columns: 1fr;
  }

  .highlights-grid {
    grid-template-columns: 1fr;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-card,
.highlight-item {
  animation: fadeInUp 0.6s ease forwards;
}

.feature-card:nth-child(2) {
  animation-delay: 0.1s;
}

.feature-card:nth-child(3) {
  animation-delay: 0.2s;
}

.feature-card:nth-child(4) {
  animation-delay: 0.3s;
}

.feature-card:nth-child(5) {
  animation-delay: 0.4s;
}

.feature-card:nth-child(6) {
  animation-delay: 0.5s;
}
</style>