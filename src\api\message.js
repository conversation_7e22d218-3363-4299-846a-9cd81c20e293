import request from '@/utils/request'

// 获取消息记录
export function getMessageList(sessionId) {
  return request({
    url: `/chatMessage/getMessageList/${sessionId}`,
    method: 'get'
  })
}

// 获取指定会话的最新N条消息
export function getLatestMessages(sessionId, limit = 10) {
  return request({
    url: `/chatMessage/getLatestMessages/${sessionId}`,
    method: 'get',
    params: {
      limit: limit
    }
  })
}

// 获取指定会话的消息数量
export function getMessageCount(sessionId) {
  return request({
    url: `/chatMessage/getMessageCount/${sessionId}`,
    method: 'get'
  })
}

// 删除指定会话的所有消息
export function deleteMessages(sessionId) {
  return request({
    url: `/chatMessage/deleteMessages/${sessionId}`,
    method: 'delete'
  })
}