import request from '@/utils/request'

// 图片上传
export function uploadImage(file) {
  const formData = new FormData()
  formData.append('file', file)

  return request({
    url: '/upload/image',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 文本文件上传
export function uploadText(file) {
  const formData = new FormData()
  formData.append('file', file)

  return request({
    url: '/upload/text',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}